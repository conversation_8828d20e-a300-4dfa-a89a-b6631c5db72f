import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import Database from 'better-sqlite3';
import fs from 'node:fs';
import path from 'node:path';

const TMP_DIR = path.resolve(process.cwd(), '.tmp-tests');
const DB_FILE = path.join(TMP_DIR, 'fts-test.db');

describe('FTS5 voters_fts (integration)', () => {
  let sqlite: Database.Database;

  beforeAll(() => {
    if (!fs.existsSync(TMP_DIR)) fs.mkdirSync(TMP_DIR, { recursive: true });
    if (fs.existsSync(DB_FILE)) fs.rmSync(DB_FILE);
    sqlite = new Database(DB_FILE);

    // Enable required pragmas
    sqlite.pragma('journal_mode = WAL');
    sqlite.pragma('synchronous = NORMAL');
    sqlite.pragma('foreign_keys = ON');

    // Create schema with working FTS setup
    sqlite.exec(`
      PRAGMA foreign_keys = ON;

      CREATE TABLE voters (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        relationship_type TEXT NOT NULL,
        relationship_name TEXT NOT NULL,
        gender TEXT NOT NULL,
        birth_year INTEGER,
        epic_number TEXT NOT NULL,
        house_number TEXT NOT NULL,
        polling_station_id TEXT NOT NULL,
        status TEXT NOT NULL,
        phone TEXT,
        custom_notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );

      -- FTS virtual table without external content (standalone)
      CREATE VIRTUAL TABLE voters_fts USING fts5(
        voter_id UNINDEXED,
        name,
        relationship_name,
        epic_number,
        house_number,
        phone,
        custom_notes
      );

      -- Triggers to keep FTS in sync
      CREATE TRIGGER voters_fts_ai AFTER INSERT ON voters BEGIN
        INSERT INTO voters_fts(voter_id, name, relationship_name, epic_number, house_number, phone, custom_notes)
        VALUES (new.id, new.name, new.relationship_name, new.epic_number, new.house_number, new.phone, new.custom_notes);
      END;

      CREATE TRIGGER voters_fts_ad AFTER DELETE ON voters BEGIN
        DELETE FROM voters_fts WHERE voter_id = old.id;
      END;

      CREATE TRIGGER voters_fts_au AFTER UPDATE ON voters BEGIN
        DELETE FROM voters_fts WHERE voter_id = old.id;
        INSERT INTO voters_fts(voter_id, name, relationship_name, epic_number, house_number, phone, custom_notes)
        VALUES (new.id, new.name, new.relationship_name, new.epic_number, new.house_number, new.phone, new.custom_notes);
      END;
    `);
  });

  afterAll(() => {
    sqlite?.close();
    if (fs.existsSync(DB_FILE)) fs.rmSync(DB_FILE);
  });

  it('indexes inserted voters and matches by name, epic, and house_number', () => {
    const now = new Date().toISOString();

    // Insert voters (triggers should populate FTS)
    const insertVoter = sqlite.prepare(`
      INSERT INTO voters (
        id,name,relationship_type,relationship_name,gender,birth_year,epic_number,house_number,
        polling_station_id,status,phone,custom_notes,created_at,updated_at
      ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
    `);

    // Insert test data
    insertVoter.run('01VOTER01', 'Alice Johnson', 'Father', 'Robert', 'Female', 1988, '**********', 'H-12', 'PS1', 'Active', '555-0001', 'Note A', now, now);
    insertVoter.run('01VOTER02', 'Bob Stone', 'Mother', 'Evelyn', 'Male', 1985, '**********', 'H-34', 'PS1', 'Active', '555-0002', 'Note B', now, now);
    insertVoter.run('01VOTER03', 'Carol Smith', 'Husband', 'Aaron', 'Female', 1990, '**********', '12A', 'PS1', 'Active', '555-0003', 'House Twelve A', now, now);

    // Match by name
    const matchByName = sqlite.prepare(`SELECT voter_id, name FROM voters_fts WHERE voters_fts MATCH ?`).all('Alice') as Array<{ voter_id: string; name: string }>;
    expect(matchByName.map(r => r.voter_id)).toContain('01VOTER01');

    // Match by EPIC
    const matchByEpic = sqlite.prepare(`SELECT voter_id, epic_number FROM voters_fts WHERE voters_fts MATCH ?`).all('**********') as Array<{ voter_id: string; epic_number: string }>;
    expect(matchByEpic.map(r => r.voter_id)).toContain('01VOTER01');

    // Match by house_number with quotes for exact match
    const matchByHouse = sqlite.prepare(`SELECT voter_id, house_number FROM voters_fts WHERE voters_fts MATCH ?`).all('"12A"') as Array<{ voter_id: string; house_number: string }>;
    expect(matchByHouse.map(r => r.voter_id)).toContain('01VOTER03');
  });

  it('updates FTS entries on UPDATE and DELETE via triggers', () => {
    // Update name for 01VOTER01 and ensure search reflects change
    sqlite.prepare(`UPDATE voters SET name = ? WHERE id = ?`).run('Alice Johnson-Smith', '01VOTER01');

    // Search for the updated name
    const afterUpdate = sqlite.prepare(`SELECT voter_id, name FROM voters_fts WHERE voters_fts MATCH ?`).all('"Johnson-Smith"') as Array<{ voter_id: string; name: string }>;
    expect(afterUpdate.map(r => r.voter_id)).toContain('01VOTER01');

    // Delete voter and ensure FTS entry is removed
    sqlite.prepare(`DELETE FROM voters WHERE id = ?`).run('01VOTER02');

    const afterDelete = sqlite.prepare(`SELECT voter_id FROM voters_fts WHERE voter_id = ?`).all('01VOTER02') as Array<{ voter_id: string }>;
    expect(afterDelete.length).toBe(0);
  });
});