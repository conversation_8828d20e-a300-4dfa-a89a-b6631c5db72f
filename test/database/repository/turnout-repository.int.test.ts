import { afterAll, afterEach, beforeAll, describe, expect, test } from 'vitest'
import Database from 'better-sqlite3'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import { sql, eq } from 'drizzle-orm'
// NOTE: In integration tests we avoid path alias to keep Vitest config-agnostic
import TurnoutRepositorySqlite from '../../../src/main/repositories/turnout-repository'
import { voters, voterTurnout, sections, pollingStations } from '../../../src/main/database/schema'

function ulid() {
  // Not cryptographically strong; sufficient for tests
  return '01' + Math.random().toString(36).slice(2, 10) + Math.random().toString(36).slice(2, 10)
}

describe('TurnoutRepositorySqlite (integration)', () => {
  let db: ReturnType<typeof drizzle>
  let raw: Database.Database
  let repo: TurnoutRepositorySqlite

  beforeAll(async () => {
    raw = new Database(':memory:')
    db = drizzle(raw)
    repo = new TurnoutRepositorySqlite(db as any)

    // Create minimal schema needed for tests. Using SQL to avoid relying on external migrations.
    raw.exec(`
      PRAGMA journal_mode = WAL;

      CREATE TABLE polling_stations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        address TEXT,
        code TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT
      );

      CREATE TABLE sections (
        id TEXT PRIMARY KEY,
        polling_station_id TEXT NOT NULL,
        name TEXT NOT NULL,
        code TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT,
        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id)
      );

      CREATE TABLE voters (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        relationship_type TEXT,
        relationship_name TEXT,
        house_number TEXT,
        birth_year INTEGER,
        gender TEXT,
        epic_number TEXT,
        polling_station_id TEXT,
        section_id TEXT,
        education TEXT,
        occupation TEXT,
        community TEXT,
        religion TEXT,
        economic_status TEXT,
        supporter_status TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT,
        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id),
        FOREIGN KEY (section_id) REFERENCES sections(id)
      );

      CREATE TABLE voter_turnout (
        id TEXT PRIMARY KEY,
        voter_id TEXT NOT NULL,
        election_year INTEGER NOT NULL,
        voted INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT,
        FOREIGN KEY (voter_id) REFERENCES voters(id)
      );

      -- Soft-delete aware unique index per requirements
      CREATE UNIQUE INDEX idx_turnout_voter_year
      ON voter_turnout(voter_id, election_year)
      WHERE deleted_at IS NULL;
    `)
  })

  afterEach(async () => {
    // Truncate tables between tests
    raw.exec(`
      DELETE FROM voter_turnout;
      DELETE FROM voters;
      DELETE FROM sections;
      DELETE FROM polling_stations;
    `)
  })

  afterAll(async () => {
    raw.close()
  })

  async function seedStationSection() {
    const now = new Date().toISOString()
    const ps1 = ulid()
    const ps2 = ulid()
    const sec1 = ulid()
    const sec2 = ulid()

    await db.insert(pollingStations).values([
      { id: ps1, name: 'PS-1', createdAt: now, updatedAt: now, deletedAt: null },
      { id: ps2, name: 'PS-2', createdAt: now, updatedAt: now, deletedAt: null },
    ] as any)

    await db.insert(sections).values([
      { id: sec1, pollingStationId: ps1, name: 'S-1', createdAt: now, updatedAt: now, deletedAt: null },
      { id: sec2, pollingStationId: ps2, name: 'S-2', createdAt: now, updatedAt: now, deletedAt: null },
    ] as any)

    return { ps1, ps2, sec1, sec2, now }
  }

  async function seedVoter(name: string, pollingStationId: string, sectionId: string, extras?: Record<string, any>) {
    const now = new Date().toISOString()
    const id = ulid()
    // use raw SQL to avoid column name/type mapping issues in this minimal schema
    raw
      .prepare(
        `INSERT INTO voters
        (id,name,relationship_type,relationship_name,house_number,birth_year,gender,epic_number,polling_station_id,section_id,education,occupation,community,religion,economic_status,supporter_status,created_at,updated_at,deleted_at)
        VALUES (@id,@name,@relationship_type,@relationship_name,@house_number,@birth_year,@gender,@epic_number,@polling_station_id,@section_id,@education,@occupation,@community,@religion,@economic_status,@supporter_status,@created_at,@updated_at,NULL)`
      )
      .run({
        id,
        name,
        relationship_type: 'S/O',
        relationship_name: 'X',
        house_number: '12',
        birth_year: 1985,
        gender: (extras?.gender as string) ?? 'M',
        epic_number: (extras?.epicNumber as string) ?? '**********',
        polling_station_id: pollingStationId,
        section_id: sectionId,
        education: 'Graduate',
        occupation: 'Engineer',
        community: 'C1',
        religion: 'R1',
        economic_status: 'Middle',
        supporter_status: 'Neutral',
        created_at: now,
        updated_at: now,
      })
    return { id, now }
  }

  test('upsert inserts a new record and find returns it', async () => {
    const { ps1, sec1 } = await seedStationSection()
    const { id: voterId } = await seedVoter('A', ps1, sec1, { epicNumber: '**********' })

    const created = await repo.upsert({ voterId, electionYear: 2024, voted: true })
    expect(created.voterId).toBe(voterId)
    expect(created.electionYear).toBe(2024)
    expect(created.voted).toBe(true)

    const found = await repo.find(voterId, 2024)
    expect(found).not.toBeNull()
    expect(found!.id).toBe(created.id)
  })

  test('upsert updates when existing active record present', async () => {
    const { ps1, sec1 } = await seedStationSection()
    const { id: voterId } = await seedVoter('B', ps1, sec1, { epicNumber: '**********' })

    const first = await repo.upsert({ voterId, electionYear: 2024, voted: false })
    expect(first.voted).toBe(false)

    const updated = await repo.upsert({ voterId, electionYear: 2024, voted: true })
    expect(updated.id).toBe(first.id)
    expect(updated.voted).toBe(true)
  })

  test('soft-delete enables reinsertion for same voter/year', async () => {
    const { ps1, sec1 } = await seedStationSection()
    const { id: voterId } = await seedVoter('C', ps1, sec1, { epicNumber: '**********' })

    const first = await repo.upsert({ voterId, electionYear: 2024, voted: true })
    await repo.delete(first.id)

    // Now re-insert after soft delete should create a new row (new id)
    const second = await repo.upsert({ voterId, electionYear: 2024, voted: false })
    expect(second.id).not.toBe(first.id)
    expect(second.voted).toBe(false)
  })

  test('listByVoter returns records ordered by electionYear', async () => {
    const { ps1, sec1 } = await seedStationSection()
    const { id: voterId } = await seedVoter('D', ps1, sec1, { epicNumber: '**********' })

    await repo.upsert({ voterId, electionYear: 2029, voted: false })
    await repo.upsert({ voterId, electionYear: 2024, voted: true })
    await repo.upsert({ voterId, electionYear: 2034, voted: true })

    const list = await repo.listByVoter(voterId)
    expect(list.map(r => r.electionYear)).toEqual([2024, 2029, 2034])
  })

  test('aggregateByStation computes totals and voted counts', async () => {
    const { ps1, ps2, sec1, sec2 } = await seedStationSection()
    const v1 = await seedVoter('E', ps1, sec1, { epicNumber: '**********', gender: 'F' })
    const v2 = await seedVoter('F', ps1, sec1, { epicNumber: '**********', gender: 'M' })
    const v3 = await seedVoter('G', ps2, sec2, { epicNumber: '**********', gender: 'F' })

    await repo.upsert({ voterId: v1.id, electionYear: 2024, voted: true })
    await repo.upsert({ voterId: v2.id, electionYear: 2024, voted: false })
    await repo.upsert({ voterId: v3.id, electionYear: 2024, voted: true })

    const rows = await repo.aggregateByStation(2024)
    const by = Object.fromEntries(rows.map(r => [r.pollingStationId, r]))

    expect(by[ps1].total).toBe(2)
    expect(by[ps1].voted).toBe(1)
    expect(by[ps2].total).toBe(1)
    expect(by[ps2].voted).toBe(1)
  })

  test('aggregateBySection filters by optional pollingStationId and computes counts', async () => {
    const { ps1, ps2, sec1, sec2 } = await seedStationSection()
    const v1 = await seedVoter('H', ps1, sec1, { epicNumber: '**********' })
    const v2 = await seedVoter('I', ps1, sec1, { epicNumber: '**********' })
    const v3 = await seedVoter('J', ps2, sec2, { epicNumber: '**********' })

    await repo.upsert({ voterId: v1.id, electionYear: 2024, voted: true })
    await repo.upsert({ voterId: v2.id, electionYear: 2024, voted: false })
    await repo.upsert({ voterId: v3.id, electionYear: 2024, voted: true })

    const all = await repo.aggregateBySection(2024)
    const byAll = Object.fromEntries(all.map(r => [String(r.sectionId), r]))
    expect(byAll[String(sec1)].total).toBe(2)
    expect(byAll[String(sec1)].voted).toBe(1)
    expect(byAll[String(sec2)].total).toBe(1)
    expect(byAll[String(sec2)].voted).toBe(1)

    const onlyPs1 = await repo.aggregateBySection(2024, ps1)
    expect(onlyPs1.length).toBe(1)
    expect(onlyPs1[0].sectionId).toBe(sec1)
    expect(onlyPs1[0].total).toBe(2)
    expect(onlyPs1[0].voted).toBe(1)
  })

  test('aggregateByDemographic groups by gender and counts', async () => {
    const { ps1, sec1 } = await seedStationSection()
    const v1 = await seedVoter('K', ps1, sec1, { epicNumber: '**********', gender: 'F' })
    const v2 = await seedVoter('L', ps1, sec1, { epicNumber: '**********', gender: 'M' })
    const v3 = await seedVoter('M', ps1, sec1, { epicNumber: '**********', gender: 'F' })

    await repo.upsert({ voterId: v1.id, electionYear: 2024, voted: true })
    await repo.upsert({ voterId: v2.id, electionYear: 2024, voted: false })
    await repo.upsert({ voterId: v3.id, electionYear: 2024, voted: true })

    const rows = await repo.aggregateByDemographic(2024, 'gender')
    const by = Object.fromEntries(rows.map(r => [r.key, r]))

    expect(by['F'].total).toBe(2)
    expect(by['F'].voted).toBe(2)
    expect(by['M'].total).toBe(1)
    expect(by['M'].voted).toBe(0)
  })
})