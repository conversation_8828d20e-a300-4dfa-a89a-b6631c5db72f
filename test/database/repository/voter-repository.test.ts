/**
 * Unit tests for VoterRepository
 * Tests voter-specific data access operations and business rules
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { VoterRepository } from '../../../src/main/repositories/voter-repository';
import { DomainError, ErrorCode } from '../../../src/shared/types/errors';

// Mock database client
const mockDbClient = {
  voters: {
    findFirst: vi.fn(),
    findMany: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
  voters_fts: {
    search: vi.fn(),
  },
};

// Mock ULID generator
const mockGenerateId = vi.fn().mockReturnValue('test-voter-id');

// Test implementation
class TestVoterRepository extends VoterRepository {
  constructor() {
    super(mockDbClient as any, mockGenerateId);
  }
}

describe('VoterRepository', () => {
  let repository: TestVoterRepository;

  beforeEach(() => {
    vi.clearAllMocks();
    repository = new TestVoterRepository();
  });

  describe('findById', () => {
    it('should return a voter by ID', async () => {
      const mockVoter = {
        id: 'test-voter-id',
        name: 'John Doe',
        epic_number: '**********',
        status: 'Active',
      };
      mockDbClient.voters.findFirst.mockResolvedValue(mockVoter);

      const result = await repository.findById('test-voter-id');

      expect(result).toEqual(mockVoter);
      expect(mockDbClient.voters.findFirst).toHaveBeenCalledWith({
        where: { id: 'test-voter-id', deleted_at: null },
      });
    });

    it('should return null when voter not found', async () => {
      mockDbClient.voters.findFirst.mockResolvedValue(null);

      const result = await repository.findById('non-existent-id');

      expect(result).toBeNull();
    });

    it('should filter out soft-deleted voters', async () => {
      // The base repository should already filter out soft-deleted records
      // This test verifies the behavior by checking that the query includes deleted_at: null
      const mockVoter = { id: 'test-voter-id', name: 'John Doe' };
      mockDbClient.voters.findFirst.mockResolvedValue(mockVoter);

      const result = await repository.findById('test-voter-id');

      expect(result).toEqual(mockVoter);
      expect(mockDbClient.voters.findFirst).toHaveBeenCalledWith({
        where: { id: 'test-voter-id', deleted_at: null },
      });
    });
  });

  describe('findByEpicNumber', () => {
    it('should return a voter by EPIC number', async () => {
      const mockVoter = {
        id: 'test-voter-id',
        name: 'John Doe',
        epic_number: '**********',
        status: 'Active',
      };
      mockDbClient.voters.findFirst.mockResolvedValue(mockVoter);

      const result = await repository.findByEpicNumber('**********');

      expect(result).toEqual(mockVoter);
      expect(mockDbClient.voters.findFirst).toHaveBeenCalledWith({
        where: { epic_number: '**********', deleted_at: null },
      });
    });

    it('should return null when EPIC number not found', async () => {
      mockDbClient.voters.findFirst.mockResolvedValue(null);

      const result = await repository.findByEpicNumber('**********');

      expect(result).toBeNull();
    });

    it('should ensure EPIC number uniqueness among active voters', async () => {
      const mockVoter = {
        id: 'test-voter-id',
        name: 'John Doe',
        epic_number: '**********',
        status: 'Active',
      };
      mockDbClient.voters.findFirst.mockResolvedValue(mockVoter);

      const result = await repository.findByEpicNumber('**********');

      expect(result).toEqual(mockVoter);
      // Verify that the query excludes soft-deleted records
      expect(mockDbClient.voters.findFirst).toHaveBeenCalledWith({
        where: { epic_number: '**********', deleted_at: null },
      });
    });
  });

  describe('findByPollingStation', () => {
    it('should return voters for a polling station', async () => {
      const mockVoters = [
        { id: '1', name: 'Voter 1', polling_station_id: 'station-1' },
        { id: '2', name: 'Voter 2', polling_station_id: 'station-1' },
      ];
      mockDbClient.voters.findMany.mockResolvedValue(mockVoters);

      const result = await repository.findByPollingStation('station-1');

      expect(result).toEqual(mockVoters);
      expect(mockDbClient.voters.findMany).toHaveBeenCalledWith({
        where: { polling_station_id: 'station-1', deleted_at: null },
      });
    });

    it('should return empty array when no voters found', async () => {
      mockDbClient.voters.findMany.mockResolvedValue([]);

      const result = await repository.findByPollingStation('station-1');

      expect(result).toEqual([]);
    });

    it('should support pagination', async () => {
      const mockVoters = [{ id: '1', name: 'Voter 1' }];
      mockDbClient.voters.findMany.mockResolvedValue(mockVoters);

      await repository.findByPollingStation('station-1', { skip: 10, take: 20 });

      expect(mockDbClient.voters.findMany).toHaveBeenCalledWith({
        where: { polling_station_id: 'station-1', deleted_at: null },
        skip: 10,
        take: 20,
      });
    });
  });

  describe('findBySection', () => {
    it('should return voters for a section', async () => {
      const mockVoters = [
        { id: '1', name: 'Voter 1', section_id: 'section-1' },
        { id: '2', name: 'Voter 2', section_id: 'section-1' },
      ];
      mockDbClient.voters.findMany.mockResolvedValue(mockVoters);

      const result = await repository.findBySection('section-1');

      expect(result).toEqual(mockVoters);
      expect(mockDbClient.voters.findMany).toHaveBeenCalledWith({
        where: { section_id: 'section-1', deleted_at: null },
      });
    });

    it('should return voters with null section_id for "Unassigned"', async () => {
      const mockVoters = [
        { id: '1', name: 'Voter 1', section_id: null },
        { id: '2', name: 'Voter 2', section_id: null },
      ];
      mockDbClient.voters.findMany.mockResolvedValue(mockVoters);

      const result = await repository.findBySection('unassigned');

      expect(result).toEqual(mockVoters);
      expect(mockDbClient.voters.findMany).toHaveBeenCalledWith({
        where: { section_id: null, deleted_at: null },
      });
    });
  });

  describe('search', () => {
    it('should search voters using FTS', async () => {
      const mockResults = [
        { id: '1', name: 'John Doe', rank: 100 },
        { id: '2', name: 'Jane Smith', rank: 90 },
      ];
      mockDbClient.voters_fts.search.mockResolvedValue(mockResults);

      const result = await repository.search('john');

      expect(result).toEqual(mockResults);
      expect(mockDbClient.voters_fts.search).toHaveBeenCalledWith('john');
    });

    it('should combine FTS with filters', async () => {
      const mockResults = [{ id: '1', name: 'John Doe', rank: 100 }];
      mockDbClient.voters.findMany.mockResolvedValue(mockResults);

      const result = await repository.search('john', {
        pollingStationId: 'station-1',
        status: 'Active'
      });

      expect(result).toEqual(mockResults);
      expect(mockDbClient.voters.findMany).toHaveBeenCalledWith({
        where: {
          polling_station_id: 'station-1',
          status: 'Active',
          deleted_at: null,
        },
      });
    });

    it('should handle empty search results', async () => {
      mockDbClient.voters_fts.search.mockResolvedValue([]);

      const result = await repository.search('nonexistent');

      expect(result).toEqual([]);
    });
  });

  describe('create', () => {
    it('should create a new voter with ULID', async () => {
      const voterData = {
        name: 'John Doe',
        epic_number: '**********',
        polling_station_id: 'station-1',
      };
      const mockResult = {
        id: 'test-voter-id',
        ...voterData,
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
      };
      mockDbClient.voters.create.mockResolvedValue(mockResult);
      mockDbClient.voters.findFirst.mockResolvedValue(null); // No existing voter with this EPIC
      mockGenerateId.mockReturnValue('test-voter-id');

      const result = await repository.create(voterData);

      expect(result).toEqual(mockResult);
      expect(mockGenerateId).toHaveBeenCalled();
      expect(mockDbClient.voters.create).toHaveBeenCalledWith({
        data: {
          ...voterData,
          id: 'test-voter-id',
          created_at: expect.any(String),
          updated_at: expect.any(String),
        },
      });
    });

    it('should validate EPIC number format', async () => {
      const invalidVoterData = {
        name: 'John Doe',
        epic_number: 'invalid-epic',
        polling_station_id: 'station-1',
      };

      await expect(repository.create(invalidVoterData)).rejects.toThrow(DomainError);
      expect(mockDbClient.voters.create).not.toHaveBeenCalled();
    });

    it('should validate required fields', async () => {
      const invalidVoterData = {
        name: 'John Doe',
        // Missing required polling_station_id
      };

      await expect(repository.create(invalidVoterData)).rejects.toThrow(DomainError);
    });

    it('should check for duplicate EPIC numbers', async () => {
      const voterData = {
        name: 'John Doe',
        epic_number: '**********',
        polling_station_id: 'station-1',
      };
      mockDbClient.voters.findFirst.mockResolvedValue({ id: 'existing-id', epic_number: '**********' });

      await expect(repository.create(voterData)).rejects.toThrow(ErrorCode.CONFLICT);
    });
  });

  describe('update', () => {
    it('should update voter data', async () => {
      const updateData = { name: 'Updated Name' };
      const mockResult = {
        id: 'test-voter-id',
        name: 'Updated Name',
        updated_at: '2023-01-01T00:00:00.000Z',
      };
      mockDbClient.voters.update.mockResolvedValue(mockResult);

      const result = await repository.update('test-voter-id', updateData);

      expect(result).toEqual(mockResult);
      expect(mockDbClient.voters.update).toHaveBeenCalledWith({
        where: { id: 'test-voter-id' },
        data: {
          ...updateData,
          updated_at: expect.any(String),
        },
      });
    });

    it('should validate EPIC number format on update', async () => {
      const invalidUpdateData = { epic_number: 'invalid-epic' };

      await expect(repository.update('test-voter-id', invalidUpdateData)).rejects.toThrow(DomainError);
    });

    it('should check for duplicate EPIC numbers on update', async () => {
      const updateData = { epic_number: '**********' };
      mockDbClient.voters.findFirst.mockResolvedValue({
        id: 'different-id',
        epic_number: '**********',
        deleted_at: null
      });

      await expect(repository.update('test-voter-id', updateData)).rejects.toThrow(ErrorCode.CONFLICT);
    });
  });

  describe('softDelete', () => {
    it('should soft delete a voter', async () => {
      mockDbClient.voters.update.mockResolvedValue({});

      await repository.softDelete('test-voter-id');

      expect(mockDbClient.voters.update).toHaveBeenCalledWith({
        where: { id: 'test-voter-id' },
        data: { deleted_at: expect.any(String) },
      });
    });

    it('should handle soft delete for Duplicate/Disqualified status', async () => {
      const voter = {
        id: 'test-voter-id',
        status: 'Duplicate',
      };
      mockDbClient.voters.findFirst.mockResolvedValue(voter);

      await repository.softDelete('test-voter-id');

      expect(mockDbClient.voters.update).toHaveBeenCalledWith({
        where: { id: 'test-voter-id' },
        data: { deleted_at: expect.any(String) },
      });
    });
  });

  describe('findByStatus', () => {
    it('should return voters by status', async () => {
      const mockVoters = [
        { id: '1', name: 'Voter 1', status: 'Active' },
        { id: '2', name: 'Voter 2', status: 'Active' },
      ];
      mockDbClient.voters.findMany.mockResolvedValue(mockVoters);

      const result = await repository.findByStatus('Active');

      expect(result).toEqual(mockVoters);
      expect(mockDbClient.voters.findMany).toHaveBeenCalledWith({
        where: { status: 'Active', deleted_at: null },
      });
    });

    it('should handle multiple statuses', async () => {
      const mockVoters = [{ id: '1', name: 'Voter 1', status: 'Expired' }];
      mockDbClient.voters.findMany.mockResolvedValue(mockVoters);

      const result = await repository.findByStatus(['Expired', 'Shifted']);

      expect(result).toEqual(mockVoters);
      expect(mockDbClient.voters.findMany).toHaveBeenCalledWith({
        where: {
          status: { in: ['Expired', 'Shifted'] },
          deleted_at: null,
        },
      });
    });
  });

  describe('getStatistics', () => {
    it('should return voter statistics', async () => {
      const mockStats = {
        total: 100,
        active: 80,
        expired: 10,
        shifted: 5,
        missing: 3,
        duplicate: 1,
        disqualified: 1,
      };
      mockDbClient.voters.findMany.mockImplementation((query) => {
        if (query.where.status === 'Active') return Promise.resolve(Array(80).fill({}));
        if (query.where.status === 'Expired') return Promise.resolve(Array(10).fill({}));
        if (query.where.status === 'Shifted') return Promise.resolve(Array(5).fill({}));
        if (query.where.status === 'Missing') return Promise.resolve(Array(3).fill({}));
        if (query.where.status === 'Duplicate') return Promise.resolve(Array(1).fill({}));
        if (query.where.status === 'Disqualified') return Promise.resolve(Array(1).fill({}));
        return Promise.resolve(Array(100).fill({}));
      });

      const result = await repository.getStatistics();

      expect(result).toEqual(mockStats);
    });
  });

  describe('validation', () => {
    it('should validate birth year range', async () => {
      const invalidVoterData = {
        name: 'John Doe',
        epic_number: '**********',
        polling_station_id: 'station-1',
        birth_year: 1800, // Too old
      };

      await expect(repository.create(invalidVoterData)).rejects.toThrow(DomainError);
    });

    it('should validate supporter status against config', async () => {
      const invalidVoterData = {
        name: 'John Doe',
        epic_number: '**********',
        polling_station_id: 'station-1',
        supporter_status: 'Invalid Status',
      };

      await expect(repository.create(invalidVoterData)).rejects.toThrow(DomainError);
    });
  });
});