/**
 * Unit tests for BaseRepository interface
 * Tests the contract that all repositories must implement
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { BaseRepository } from '../../../src/main/repositories/base-repository';
import { DomainError, ErrorCode } from '../../../src/shared/types/errors';

// Mock database client
const mockDbClient = {
  findMany: vi.fn(),
  findFirst: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  execute: vi.fn(),
};

// Test implementation of BaseRepository
class TestRepository extends BaseRepository<any> {
  protected tableName = 'test_table';

  async findById(id: string): Promise<any | null> {
    try {
      const result = await mockDbClient.findFirst({
        where: { id, deleted_at: null },
      });
      return result || null;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async findMany(filters: any = {}): Promise<any[]> {
    try {
      const where = { ...filters.where, deleted_at: null };
      return mockDbClient.findMany({ where, ...filters });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async create(data: any): Promise<any> {
    try {
      const result = await mockDbClient.create({
        data: { ...data, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      });
      return result;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async update(id: string, data: any): Promise<any> {
    try {
      const result = await mockDbClient.update({
        where: { id },
        data: { ...data, updated_at: new Date().toISOString() },
      });
      return result;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async softDelete(id: string): Promise<void> {
    try {
      await mockDbClient.update({
        where: { id },
        data: { deleted_at: new Date().toISOString() },
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async hardDelete(id: string): Promise<void> {
    try {
      await mockDbClient.delete({
        where: { id },
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }
}

describe('BaseRepository', () => {
  let repository: TestRepository;

  beforeEach(() => {
    vi.clearAllMocks();
    repository = new TestRepository();
  });

  describe('findById', () => {
    it('should return a record by ID', async () => {
      const mockRecord = { id: 'test-id', name: 'Test Record' };
      mockDbClient.findFirst.mockResolvedValue(mockRecord);

      const result = await repository.findById('test-id');

      expect(result).toEqual(mockRecord);
      expect(mockDbClient.findFirst).toHaveBeenCalledWith({
        where: { id: 'test-id', deleted_at: null },
      });
    });

    it('should return null when record not found', async () => {
      mockDbClient.findFirst.mockResolvedValue(null);

      const result = await repository.findById('non-existent-id');

      expect(result).toBeNull();
      expect(mockDbClient.findFirst).toHaveBeenCalledWith({
        where: { id: 'non-existent-id', deleted_at: null },
      });
    });

    it('should handle database errors', async () => {
      const error = new Error('Database connection failed');
      mockDbClient.findFirst.mockRejectedValue(error);

      await expect(repository.findById('test-id')).rejects.toThrow(DomainError);
    });
  });

  describe('findMany', () => {
    it('should return multiple records with default filters', async () => {
      const mockRecords = [
        { id: '1', name: 'Record 1' },
        { id: '2', name: 'Record 2' },
      ];
      mockDbClient.findMany.mockResolvedValue(mockRecords);

      const result = await repository.findMany();

      expect(result).toEqual(mockRecords);
      expect(mockDbClient.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
      });
    });

    it('should apply custom filters', async () => {
      const mockRecords = [{ id: '1', name: 'Record 1', status: 'active' }];
      mockDbClient.findMany.mockResolvedValue(mockRecords);

      const filters = {
        where: { status: 'active' },
        orderBy: { created_at: 'desc' },
      };

      const result = await repository.findMany(filters);

      expect(result).toEqual(mockRecords);
      expect(mockDbClient.findMany).toHaveBeenCalledWith({
        where: { status: 'active' },
        orderBy: { created_at: 'desc' },
      });
    });

    it('should handle empty results', async () => {
      mockDbClient.findMany.mockResolvedValue([]);

      const result = await repository.findMany();

      expect(result).toEqual([]);
      expect(mockDbClient.findMany).toHaveBeenCalled();
    });
  });

  describe('create', () => {
    it('should create a new record with timestamps', async () => {
      const inputData = { name: 'New Record', value: 123 };
      const mockResult = { id: 'new-id', ...inputData, created_at: '2023-01-01T00:00:00.000Z', updated_at: '2023-01-01T00:00:00.000Z' };
      mockDbClient.create.mockResolvedValue(mockResult);

      const result = await repository.create(inputData);

      expect(result).toEqual(mockResult);
      expect(mockDbClient.create).toHaveBeenCalledWith({
        data: {
          ...inputData,
          created_at: expect.any(String),
          updated_at: expect.any(String),
        },
      });
    });

    it('should handle creation errors', async () => {
      const error = new Error('Constraint violation');
      mockDbClient.create.mockRejectedValue(error);

      await expect(repository.create({ name: 'Test' })).rejects.toThrow(DomainError);
    });
  });

  describe('update', () => {
    it('should update an existing record', async () => {
      const updateData = { name: 'Updated Name' };
      const mockResult = { id: 'test-id', name: 'Updated Name', updated_at: '2023-01-01T00:00:00.000Z' };
      mockDbClient.update.mockResolvedValue(mockResult);

      const result = await repository.update('test-id', updateData);

      expect(result).toEqual(mockResult);
      expect(mockDbClient.update).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        data: {
          ...updateData,
          updated_at: expect.any(String),
        },
      });
    });

    it('should handle update errors', async () => {
      const error = new Error('Record not found');
      mockDbClient.update.mockRejectedValue(error);

      await expect(repository.update('non-existent-id', { name: 'Test' })).rejects.toThrow(DomainError);
    });
  });

  describe('softDelete', () => {
    it('should soft delete a record by setting deleted_at timestamp', async () => {
      mockDbClient.update.mockResolvedValue({});

      await repository.softDelete('test-id');

      expect(mockDbClient.update).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        data: { deleted_at: expect.any(String) },
      });
    });

    it('should handle soft delete errors', async () => {
      const error = new Error('Database error');
      mockDbClient.update.mockRejectedValue(error);

      await expect(repository.softDelete('test-id')).rejects.toThrow(DomainError);
    });
  });

  describe('hardDelete', () => {
    it('should permanently delete a record', async () => {
      mockDbClient.delete.mockResolvedValue({});

      await repository.hardDelete('test-id');

      expect(mockDbClient.delete).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
    });

    it('should handle hard delete errors', async () => {
      const error = new Error('Cannot delete referenced record');
      mockDbClient.delete.mockRejectedValue(error);

      await expect(repository.hardDelete('test-id')).rejects.toThrow(DomainError);
    });
  });

  // Transaction support test removed - will be implemented in specific repository implementations

  describe('error handling', () => {
    it('should convert database errors to DomainError', async () => {
      const dbError = new DomainError(ErrorCode.CONFLICT, 'Unique constraint violation');
      mockDbClient.create.mockRejectedValue(dbError);

      await expect(repository.create({ name: 'Test' })).rejects.toThrow(DomainError);
    });

    it('should preserve error details in DomainError', async () => {
      const dbError = new Error('Foreign key constraint failed');
      mockDbClient.create.mockRejectedValue(dbError);

      try {
        await repository.create({ name: 'Test' });
      } catch (error) {
        expect(error).toBeInstanceOf(DomainError);
        expect(error.code).toBe(ErrorCode.TRANSIENT_FAILURE);
        expect(error.details).toEqual({ originalError: 'Error' });
      }
    });
  });

  describe('pagination support', () => {
    it('should handle pagination parameters', async () => {
      const mockRecords = [{ id: '1' }, { id: '2' }];
      mockDbClient.findMany.mockResolvedValue(mockRecords);

      const filters = {
        skip: 10,
        take: 20,
        orderBy: { created_at: 'desc' },
      };

      await repository.findMany(filters);

      expect(mockDbClient.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
        skip: 10,
        take: 20,
        orderBy: { created_at: 'desc' },
      });
    });
  });

  describe('query building', () => {
    it('should build complex queries with multiple conditions', async () => {
      const mockRecords = [{ id: '1', status: 'active' }];
      mockDbClient.findMany.mockResolvedValue(mockRecords);

      const filters = {
        where: {
          status: 'active',
          created_at: { gte: '2023-01-01' },
          OR: [
            { name: { contains: 'test' } },
            { description: { contains: 'test' } },
          ],
        },
        include: { related: true },
      };

      await repository.findMany(filters);

      expect(mockDbClient.findMany).toHaveBeenCalledWith({
        where: {
          status: 'active',
          created_at: { gte: '2023-01-01' },
          OR: [
            { name: { contains: 'test' } },
            { description: { contains: 'test' } },
          ],
        },
        include: { related: true },
      });
    });
  });
});