import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { sql } from 'drizzle-orm';
import fs from 'node:fs';
import path from 'node:path';

// This test validates DB pragmas, core tables, and key constraints with a temp DB.
// It is test-first; migrations should be implemented to satisfy these specs.

const TMP_DIR = path.resolve(process.cwd(), '.tmp-tests');
const DB_FILE = path.join(TMP_DIR, 'schema-test.db');

describe('Database schema (integration)', () => {
  let sqlite: Database.Database;
  let db: ReturnType<typeof drizzle>;

  beforeAll(() => {
    if (!fs.existsSync(TMP_DIR)) fs.mkdirSync(TMP_DIR, { recursive: true });
    if (fs.existsSync(DB_FILE)) fs.rmSync(DB_FILE);
    sqlite = new Database(DB_FILE);
    db = drizzle(sqlite);

    // Pragmas required by design
    sqlite.pragma('journal_mode = WAL');
    sqlite.pragma('synchronous = NORMAL');
    sqlite.pragma('foreign_keys = ON');

    // Create minimal tables to express constraints (will be replaced by migrations)
    sqlite.exec(`
      PRAGMA foreign_keys = ON;

      CREATE TABLE polling_stations (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT
      );

      CREATE TABLE sections (
        id TEXT PRIMARY KEY NOT NULL,
        polling_station_id TEXT NOT NULL,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT,
        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id) ON UPDATE CASCADE ON DELETE RESTRICT
      );

      CREATE TABLE voters (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        relationship_type TEXT NOT NULL,
        relationship_name TEXT NOT NULL,
        gender TEXT NOT NULL,
        birth_year INTEGER,
        epic_number TEXT NOT NULL,
        house_number TEXT NOT NULL,
        polling_station_id TEXT NOT NULL,
        section_id TEXT,
        phone TEXT,
        email TEXT,
        facebook TEXT,
        instagram TEXT,
        twitter TEXT,
        status TEXT NOT NULL,
        supporter_status TEXT,
        education TEXT,
        occupation TEXT,
        community TEXT,
        religion TEXT,
        economic_status TEXT,
        custom_notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT,
        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id) ON UPDATE CASCADE ON DELETE RESTRICT,
        FOREIGN KEY (section_id) REFERENCES sections(id) ON UPDATE CASCADE ON DELETE SET NULL
      );

      CREATE TABLE voter_turnout (
        id TEXT PRIMARY KEY NOT NULL,
        voter_id TEXT NOT NULL,
        election_year INTEGER NOT NULL,
        voted INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT,
        FOREIGN KEY (voter_id) REFERENCES voters(id) ON UPDATE CASCADE ON DELETE RESTRICT
      );

      CREATE TABLE transactions (
        id TEXT PRIMARY KEY NOT NULL,
        voter_id TEXT NOT NULL,
        transaction_date TEXT NOT NULL,
        purpose TEXT NOT NULL,
        amount INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT,
        FOREIGN KEY (voter_id) REFERENCES voters(id) ON UPDATE CASCADE ON DELETE RESTRICT
      );

      CREATE TABLE config_options (
        id TEXT PRIMARY KEY NOT NULL,
        category TEXT NOT NULL,
        value TEXT NOT NULL,
        display_name TEXT NOT NULL,
        display_order INTEGER NOT NULL,
        active INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT
      );

      CREATE TABLE users (
        id TEXT PRIMARY KEY NOT NULL,
        username TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT NOT NULL,
        full_name TEXT,
        active INTEGER NOT NULL,
        last_login TEXT,
        session_expires TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        deleted_at TEXT
      );

      -- Soft-delete-aware unique examples (partial via WHERE)
      CREATE UNIQUE INDEX idx_users_username ON users(username) WHERE deleted_at IS NULL;
      CREATE UNIQUE INDEX idx_voters_epic ON voters(epic_number) WHERE deleted_at IS NULL;

      -- Performance indexes
      CREATE INDEX idx_voters_polling_station ON voters(polling_station_id) WHERE deleted_at IS NULL;
      CREATE INDEX idx_voters_section ON voters(section_id) WHERE deleted_at IS NULL;
      CREATE UNIQUE INDEX idx_turnout_voter_year ON voter_turnout(voter_id, election_year) WHERE deleted_at IS NULL;
    `);
  });

  afterAll(() => {
    sqlite?.close();
    if (fs.existsSync(DB_FILE)) fs.rmSync(DB_FILE);
  });

  it('enables required pragmas (WAL, foreign keys)', () => {
    const wal = sqlite.pragma('journal_mode', { simple: true });
    expect(String(wal).toUpperCase()).toBe('WAL');

    const fk = sqlite.pragma('foreign_keys', { simple: true });
    expect(Number(fk)).toBe(1);
  });

  it('creates core tables', async () => {
    const tables = await db.all<{ name: string }>(sql`SELECT name FROM sqlite_master WHERE type='table'`);
    const names = tables.map(t => t.name);
    expect(names).toEqual(
      expect.arrayContaining([
        'polling_stations',
        'sections',
        'voters',
        'voter_turnout',
        'transactions',
        'config_options',
        'users'
      ])
    );
  });

  it('enforces soft-delete-aware unique EPIC among active voters', () => {
    const now = new Date().toISOString();

    // prerequisites: station + section (optional)
    sqlite
      .prepare(`INSERT INTO polling_stations (id,name,code,created_at,updated_at) VALUES (?,?,?,?,?)`)
      .run('PS1', 'Station 1', 'S1', now, now);

    // insert first voter with valid FK
    sqlite
      .prepare(
        `INSERT INTO voters (id,name,relationship_type,relationship_name,gender,birth_year,epic_number,house_number,
          polling_station_id,status,created_at,updated_at)
         VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`
      )
      .run('01HAAAAAA', 'Alice', 'Father', 'Bob', 'Female', 1990, '**********', '12', 'PS1', 'Active', now, now);

    // Inserting duplicate EPIC active should fail due to unique index WHERE deleted_at IS NULL
    expect(() =>
      sqlite
        .prepare(
          `INSERT INTO voters (id,name,relationship_type,relationship_name,gender,birth_year,epic_number,house_number,
            polling_station_id,status,created_at,updated_at)
           VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`
        )
        .run('01HAAA AAB', 'Carol', 'Father', 'Dan', 'Female', 1991, '**********', '34', 'PS1', 'Active', now, now)
    ).toThrow();

    // Soft-delete the first record, then duplicate EPIC is allowed
    sqlite.prepare(`UPDATE voters SET deleted_at = ? WHERE id = ?`).run(now, '01HAAAAAA');
    expect(() =>
      sqlite
        .prepare(
          `INSERT INTO voters (id,name,relationship_type,relationship_name,gender,birth_year,epic_number,house_number,
            polling_station_id,status,created_at,updated_at)
           VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`
        )
        .run('01HAAA AAC', 'Eve', 'Father', 'Frank', 'Female', 1992, '**********', '56', 'PS1', 'Active', now, now)
    ).not.toThrow();
  });

  it('enforces voter_turnout uniqueness per voter per election year (soft-delete-aware)', () => {
    const now = new Date().toISOString();

    // Ensure station and voter exist with valid FK
    sqlite
      .prepare(`INSERT OR IGNORE INTO polling_stations (id,name,code,created_at,updated_at) VALUES (?,?,?,?,?)`)
      .run('PS1', 'Station 1', 'S1', now, now);

    sqlite
      .prepare(
        `INSERT OR IGNORE INTO voters (id,name,relationship_type,relationship_name,gender,birth_year,epic_number,house_number,
          polling_station_id,status,created_at,updated_at)
         VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`
      )
      .run('01HVOTER01', 'Test Voter', 'Father', 'Rel', 'Male', 1980, '**********', '1', 'PS1', 'Active', now, now);

    // First turnout record ok
    sqlite
      .prepare(
        `INSERT INTO voter_turnout (id,voter_id,election_year,voted,created_at,updated_at)
         VALUES (?,?,?,?,?,?)`
      )
      .run('01HTURNOUT1', '01HVOTER01', 2024, 1, now, now);

    // Duplicate (same voter/year) should fail
    expect(() =>
      sqlite
        .prepare(
          `INSERT INTO voter_turnout (id,voter_id,election_year,voted,created_at,updated_at)
           VALUES (?,?,?,?,?,?)`
        )
        .run('01HTURNOUT2', '01HVOTER01', 2024, 0, now, now)
    ).toThrow();

    // Soft delete previous, allow reinsert
    sqlite.prepare(`UPDATE voter_turnout SET deleted_at = ? WHERE id = ?`).run(now, '01HTURNOUT1');
    expect(() =>
      sqlite
        .prepare(
          `INSERT INTO voter_turnout (id,voter_id,election_year,voted,created_at,updated_at)
           VALUES (?,?,?,?,?,?)`
        )
        .run('01HTURNOUT3', '01HVOTER01', 2024, 1, now, now)
    ).not.toThrow();
  });

  it('maintains foreign key integrity between sections and polling stations', () => {
    const now = new Date().toISOString();

    // Ensure clean station id for this test to avoid unique conflict if previous tests inserted PS1
    sqlite.prepare(`INSERT OR IGNORE INTO polling_stations (id,name,code,created_at,updated_at) VALUES (?,?,?,?,?)`)
      .run('PS_FK', 'Station FK', 'SFK', now, now);

    // Insert section linked to station
    expect(() =>
      sqlite
        .prepare(
          `INSERT INTO sections (id,polling_station_id,name,code,created_at,updated_at)
           VALUES (?,?,?,?,?,?)`
        )
        .run('SEC_FK1', 'PS_FK', 'Section 1', '1', now, now)
    ).not.toThrow();

    // Invalid section referencing non-existent station should fail
    expect(() =>
      sqlite
        .prepare(
          `INSERT INTO sections (id,polling_station_id,name,code,created_at,updated_at)
           VALUES (?,?,?,?,?,?)`
        )
        .run('SEC_BAD', 'PS_UNKNOWN', 'Bad', 'X', now, now)
    ).toThrow();
  });
});