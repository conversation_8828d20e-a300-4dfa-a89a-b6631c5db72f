import { describe, it, expect } from 'vitest';
import { extractPollingStation, extractSection, normalizeGender } from '../../src/main/workers/import/csv-schemas';

describe('csv-schemas helpers', () => {
  describe('extractPollingStation', () => {
    it('extracts code and name from "55 - LIARBANG"', () => {
      const res = extractPollingStation('55 - LIARBANG');
      expect(res.pollingStationCode).toBe(55);
      expect(res.pollingStationName).toBe('LIARBANG');
    });

    it('handles missing hyphen format gracefully', () => {
      const res = extractPollingStation('LIARBANG ONLY');
      expect(res.pollingStationCode).toBeNull();
      expect(res.pollingStationName).toBe('LIARBANG ONLY');
    });

    it('trims spaces around hyphen', () => {
      const res = extractPollingStation('  12   -   ABC  ');
      expect(res.pollingStationCode).toBe(12);
      expect(res.pollingStationName).toBe('ABC');
    });
  });

  describe('extractSection', () => {
    it('extracts number and name from "1-LIARBANG"', () => {
      const res = extractSection('1-LIARBANG');
      expect(res.sectionNumber).toBe(1);
      expect(res.sectionName).toBe('LIARBANG');
    });

    it('accepts "Unassigned" with null number', () => {
      const res = extractSection('Unassigned');
      expect(res.sectionNumber).toBeNull();
      expect(res.sectionName).toBe('Unassigned');
    });

    it('handles unexpected format as name only', () => {
      const res = extractSection('LIARBANG');
      expect(res.sectionNumber).toBeNull();
      expect(res.sectionName).toBe('LIARBANG');
    });
  });

  describe('normalizeGender', () => {
    it('maps Male/Female to M/F', () => {
      expect(normalizeGender('Male')).toBe('M');
      expect(normalizeGender('Female')).toBe('F');
      expect(normalizeGender('m')).toBe('M');
      expect(normalizeGender('f')).toBe('F');
    });

    it('returns upper-case for unknown inputs (to be rejected by schema)', () => {
      expect(normalizeGender('unknown')).toBe('UNKNOWN');
      expect(normalizeGender('')).toBeNull();
    });
  });
});