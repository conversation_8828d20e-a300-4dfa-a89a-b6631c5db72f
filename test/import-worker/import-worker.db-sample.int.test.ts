import { describe, it, expect } from 'vitest';
import { parseCsvStream } from '../../src/main/workers/import/csv-worker';
import { readFileSync } from 'node:fs';
import { validateHeaders } from '../../src/main/workers/import/csv-schemas';

describe('CSV Import - DB_SAMPLE.csv integration', () => {
  const csvPath = 'docs/sample/DB_SAMPLE.csv';
  const importId = 'dbsample-int';

  it('validates headers and parses file with normalization, counting errors properly', async () => {
    // Header validation
    const headerLine = readFileSync(csvPath, 'utf8').split(/\r?\n/, 1)[0];
    const headers = headerLine.split(',');
    const hv = validateHeaders(headers);
    expect(hv.ok).toBe(true);

    // Collect checkpoints to ensure backpressure-safe streaming
    const checkpoints: any[] = [];
    const result = await parseCsvStream(csvPath, {
      importId,
      checkpointEvery: 5,
      onCheckpoint: (cp) => { checkpoints.push(cp); },
    });

    // Basic shape
    expect(result.importId).toBe(importId);
    expect(result.totalRows).toBeGreaterThan(0);
    expect(result.durationMs).toBeGreaterThanOrEqual(0);

    // EPIC format in sample appears alphanumeric and may or may not match strict regex;
    // Assert the pipeline completes and provides a coherent summary regardless of error count.
    expect(typeof result.errorCount).toBe('number');
    expect(result.errorCount).toBeGreaterThanOrEqual(0);

    // If there are any errors, ensure epic_number/birth_year issues (if present) are surfaced with row numbers
    if (result.errorCount > 0) {
      const epicIssues = result.errors.filter(e => e.field === 'epic_number');
      const byIssues = result.errors.filter(e => e.field === 'birth_year');
      // Not strictly required to be >0 in all datasets, but should be arrays
      expect(Array.isArray(epicIssues)).toBe(true);
      expect(Array.isArray(byIssues)).toBe(true);
      // Ensure each error carries rowNo and reason
      for (const err of result.errors) {
        expect(typeof err.rowNo).toBe('number');
        expect(typeof err.reason).toBe('string');
      }
    }

    // Duplicates metric should be reported (zero or more)
    expect(result.duplicateCount).toBeGreaterThanOrEqual(0);

    // Checkpoints emitted
    if (result.totalRows >= 5) {
      expect(checkpoints.length).toBeGreaterThan(0);
      for (const cp of checkpoints) {
        expect(cp.importId).toBe(importId);
        expect(cp.processed).toBeGreaterThan(0);
        expect(cp.updatedAt).toBeGreaterThan(0);
      }
    }
  });
});