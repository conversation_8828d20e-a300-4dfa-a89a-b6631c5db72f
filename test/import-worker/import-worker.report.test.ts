import { describe, it, expect } from 'vitest';

function buildSummary(processed: number, valid: number, invalid: number, duplicates: number) {
  const start = Date.now();
  const durationMs = Date.now() - start;
  return {
    importId: '01H00000000000000000000000', // placeholder ULID for now
    processed, valid, invalid, duplicates, durationMs,
  };
}

describe('Error report and summary (scaffold)', () => {
  it('summarizes counts and contains an importId', () => {
    const s = buildSummary(10, 8, 2, 1);
    expect(s.processed).toBe(10);
    expect(s.valid).toBe(8);
    expect(s.invalid).toBe(2);
    expect(s.duplicates).toBe(1);
    expect(typeof s.importId).toBe('string');
  });
});
