import { describe, it, expect } from 'vitest';
import fs from 'node:fs';
import path from 'node:path';
import { parse } from 'csv-parse/sync';

describe('Duplicate EPIC detection (in-file)', () => {
  const fixturesDir = path.resolve(__dirname, 'csv.fixtures');

  it('flags duplicates.csv duplicates with original and duplicate rows', () => {
    const csv = fs.readFileSync(path.join(fixturesDir, 'duplicates.csv'), 'utf8');
    const records: any[] = parse(csv, { columns: true });
    const seen = new Map<string, number>();
    const dups: { epic: string; firstRow: number; dupRow: number }[] = [];
    records.forEach((r, idx) => {
      const epic = r['epic_number'];
      if (!epic) return;
      const rowNo = idx + 2; // header is line 1
      if (seen.has(epic)) {
        dups.push({ epic, firstRow: seen.get(epic)!, dupRow: rowNo });
      } else {
        seen.set(epic, rowNo);
      }
    });
    expect(dups.length).toBeGreaterThan(0);
    expect(dups[0]).toHaveProperty('firstRow');
    expect(dups[0]).toHaveProperty('dupRow');
  });
});
