import { describe, it, expect, beforeAll } from 'vitest'
import { ImportStartRequestSchema, ImportStatusRequestSchema, ImportCancelRequestSchema } from '../../src/shared/types/ipc-import'

// We invoke electron ipc handlers by importing electron/main.ts which registers handlers on ipcMain.
// In this test environment, ipcMain.handle is mocked by the test setup that comes with the project tests.
import '../../electron/main'

declare global {
  // Provided by the project's test environment/mocks
  // eslint-disable-next-line no-var
  var __ipcInvoke__: (channel: string, payload: unknown) => Promise<any>
}

function invoke(channel: string, payload: unknown): Promise<any> {
  return globalThis.__ipcInvoke__(channel, payload)
}

describe('Import IPC Handlers', () => {
  let jobId: string

  it('Import.Start should create a job and return jobId', async () => {
    const req = {
      filePath: '/tmp/example.csv',
      mapping: {
        name: 'name',
        relation_type: 'Father',
        relation_name: '<PERSON>',
        house_number: '12',
        birth_year: 1990,
        gender: 'Male',
        epic_number: '**********',
        polling_station: 'PS001',
        section: 'S001',
      },
      options: { dryRun: true },
    }

    // validate payload with schema then invoke
    const parsed = ImportStartRequestSchema.parse(req)
    const res = await invoke('Import.Start', parsed)

    expect(res).toBeDefined()
    expect(typeof res.jobId).toBe('string')
    expect(res.jobId.length).toBeGreaterThan(0)
    jobId = res.jobId
  })

  it('Import.Status should return job status in plain DTO shape', async () => {
    const req = ImportStatusRequestSchema.parse({ id: jobId })
    const status = await invoke('Import.Status', req)

    expect(status).toBeDefined()
    expect(status.id).toBe(jobId)
    expect(['pending', 'running', 'completed', 'failed', 'canceled']).toContain(status.status)
    expect(typeof status.progress).toBe('number')
    expect(status.counters).toEqual(
      expect.objectContaining({ processed: expect.any(Number), accepted: expect.any(Number), rejected: expect.any(Number) })
    )
    expect(status.createdAt).toBeDefined()
    expect(status.updatedAt).toBeDefined()
  })

  it('Import.Cancel should cancel the job and return null', async () => {
    const req = ImportCancelRequestSchema.parse({ id: jobId })
    const res = await invoke('Import.Cancel', req)
    expect(res).toBeNull()

    // Verify status is canceled now
    const statusReq = ImportStatusRequestSchema.parse({ id: jobId })
    const status = await invoke('Import.Status', statusReq)
    expect(status.status).toBe('canceled')
    expect(status.completedAt === null || typeof status.completedAt === 'string').toBeTruthy()
  })

  it('Import.Status on unknown id should throw NOT_FOUND error in plain mode', async () => {
    const req = ImportStatusRequestSchema.parse({ id: 'does-not-exist' })
    await expect(invoke('Import.Status', req)).rejects.toThrow(/NOT_FOUND:/)
  })

  it('Import.Cancel on unknown id should throw NOT_FOUND error in plain mode', async () => {
    const req = ImportCancelRequestSchema.parse({ id: 'nope' })
    await expect(invoke('Import.Cancel', req)).rejects.toThrow(/NOT_FOUND:/)
  })
})