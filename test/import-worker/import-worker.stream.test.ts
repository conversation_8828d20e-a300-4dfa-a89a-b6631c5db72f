import { describe, it, expect } from 'vitest';
import { parse } from 'csv-parse/sync';
import fs from 'node:fs';
import path from 'node:path';

function validateHeaders(headers: string[]) {
  const required = ['name','relation_type','relation_name','house_number','birth_year','gender','epic_number','polling_station','section'];
  const missing = required.filter(h => !headers.includes(h));
  return { missing, ok: missing.length === 0 };
}

describe('CSV streaming/header validation (sync parse substitute for unit test)', () => {
  const fixturesDir = path.resolve(__dirname, 'csv.fixtures');

  it('valid.csv has all required headers', () => {
    const csv = fs.readFileSync(path.join(fixturesDir, 'valid.csv'), 'utf8');
    const records = parse(csv, { columns: true });
    const headers = Object.keys(records[0] ?? {});
    const v = validateHeaders(headers);
    expect(v.ok).toBe(true);
    expect(v.missing).toEqual([]);
  });

  it('missing-headers.csv reports missing headers', () => {
    const csv = fs.readFileSync(path.join(fixturesDir, 'missing-headers.csv'), 'utf8');
    const records = parse(csv, { columns: true });
    const headers = Object.keys(records[0] ?? {});
    const v = validateHeaders(headers);
    expect(v.ok).toBe(false);
    expect(v.missing.length).toBeGreaterThan(0);
  });
});
