import { describe, it, expect } from 'vitest'
import {
  ImportStartRequestSchema,
  ImportStatusRequestSchema,
  ImportCancelRequestSchema,
  ImportJobStatusSchema,
} from '../../src/shared/types/ipc-import'

describe('Import IPC Zod Schemas', () => {
  it('validates Import.Start request (minimal valid)', () => {
    const good = {
      filePath: '/path/to/file.csv',
      mapping: {
        name: 'name',
        relation_type: 'Father',
        relation_name: '<PERSON>',
        house_number: '12',
        birth_year: 1990,
        gender: 'Male',
        epic_number: '**********',
        polling_station: 'PS001',
        section: 'S001',
      },
      options: {},
    }
    const parsed = ImportStartRequestSchema.parse(good)
    expect(parsed.filePath).toBe(good.filePath)
    expect(parsed.mapping.name).toBe('name')
  })

  it('rejects Import.Start with missing mapping fields', () => {
    const bad = {
      filePath: '/file.csv',
      mapping: {
        // missing many required fields
        name: 'name',
      },
      options: {},
    }
    expect(() => ImportStartRequestSchema.parse(bad)).toThrow()
  })

  it('validates Import.Status request', () => {
    const good = { id: 'job-1' }
    expect(ImportStatusRequestSchema.parse(good).id).toBe('job-1')
  })

  it('rejects Import.Status with empty id', () => {
    const bad = { id: '' }
    expect(() => ImportStatusRequestSchema.parse(bad)).toThrow()
  })

  it('validates Import.Cancel request', () => {
    const good = { id: 'job-2' }
    expect(ImportCancelRequestSchema.parse(good).id).toBe('job-2')
  })

  it('validates ImportJobStatus shape', () => {
    const good = {
      id: 'job-3',
      status: 'pending',
      progress: 0,
      counters: { processed: 0, accepted: 0, rejected: 0 },
      message: null,
      errorReportPath: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      completedAt: null,
    }
    const parsed = ImportJobStatusSchema.parse(good)
    expect(parsed.status).toBe('pending')
    expect(parsed.counters.processed).toBe(0)
  })
})