import { describe, it, expect } from 'vitest';
import { zCsvRow } from '../../src/main/workers/import/csv-schemas';

describe('CSV Row Zod Schema', () => {
  it('accepts valid row', () => {
    const input = {
      name: '<PERSON>',
      relation_type: 'S/O',
      relation_name: '<PERSON>',
      house_number: '12A',
      birth_year: '1990',
      gender: 'M',
      epic_number: '**********',
      polling_station: '55 - LIARBANG',
      section: '1-LIARBANG',
    };
    const parsed = zCsvRow.parse(input);
    expect(parsed.birth_year).toBe(1990);
  });

  it('rejects invalid EPIC format', () => {
    const input = {
      name: '<PERSON>',
      relation_type: 'D/O',
      relation_name: '<PERSON>',
      house_number: '15',
      birth_year: '1985',
      gender: 'F',
      epic_number: 'A1C1234567', // invalid
      polling_station: '55 - LIARBANG',
      section: '1-LIARBANG',
    };
    const r = zCsvRow.safeParse(input);
    expect(r.success).toBe(false);
    if (!r.success) {
      expect(r.error.issues.some((e) => e.path.join('.') === 'epic_number')).toBe(true);
    }
  });

  it('rejects birth year out of range', () => {
    const currentYear = new Date().getFullYear();
    const tooYoung = {
      name: 'Kid',
      relation_type: 'S/O',
      relation_name: 'Parent',
      house_number: '1',
      birth_year: String(currentYear - 10), // too new
      gender: 'M',
      epic_number: '**********',
      polling_station: '55 - LIARBANG',
      section: '1-LIARBANG',
    };
    const r1 = zCsvRow.safeParse(tooYoung);
    expect(r1.success).toBe(false);

    const tooOld = {
      ...tooYoung,
      birth_year: '1899',
      epic_number: '**********',
    };
    const r2 = zCsvRow.safeParse(tooOld);
    expect(r2.success).toBe(false);
  });
});
