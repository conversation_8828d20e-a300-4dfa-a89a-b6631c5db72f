import { describe, it, expect, beforeEach, vi } from 'vitest'

// Capture ipcMain registrations
const handlers: Record<string, Function> = {}
vi.mock('electron', () => {
  return {
    ipcMain: {
      handle: (channel: string, handler: Function) => {
        handlers[channel] = handler
      },
    },
    app: {
      on: vi.fn(),
      whenReady: vi.fn(() => Promise.resolve()),
    },
    BrowserWindow: vi.fn(),
  }
})

// Helper like ipcRenderer.invoke
async function invoke(channel: string, payload: any) {
  const handler = handlers[channel]
  if (!handler) throw new Error(`No handler for ${channel}`)
  return handler({} as any, payload)
}

describe('Turnout IPC handlers', () => {
  beforeEach(async () => {
    // reset and reimport main to re-register handlers
    for (const k of Object.keys(handlers)) delete handlers[k]
    await vi.resetModules()
    await import('../../electron/main')
  })

  it('VoterTurnout.Upsert creates a new record', async () => {
    const res = await invoke('VoterTurnout.Upsert', {
      voter_id: 'v1',
      election_year: new Date().getFullYear(),
      voted: true,
      notes: 'first',
    })
    expect(res.success).toBe(true)
    expect(res.data).toBeTruthy()
    expect(res.data.voterId ?? res.data.voter_id).toBe('v1')
    expect(res.data.electionYear ?? res.data.election_year).toBe(new Date().getFullYear())
    expect(res.data.voted).toBe(true)
  })

  it('VoterTurnout.Upsert updates existing for same voter/year', async () => {
    const year = new Date().getFullYear()
    const first = await invoke('VoterTurnout.Upsert', {
      voter_id: 'v2',
      election_year: year,
      voted: false,
    })
    expect(first.success).toBe(true)
    expect(first.data).toBeTruthy()
    const updated = await invoke('VoterTurnout.Upsert', {
      voter_id: 'v2',
      election_year: year,
      voted: true,
    })
    expect(updated.success).toBe(true)
    expect(updated.data).toBeTruthy()
    expect(updated.data.voted).toBe(true)
    expect(updated.data.id).toBe(first.data.id)
  })

  it('VoterTurnout.GetByVoter returns ordered list asc by electionYear', async () => {
    const voterId = 'v3'
    await invoke('VoterTurnout.Upsert', { voter_id: voterId, election_year: 2024, voted: true })
    await invoke('VoterTurnout.Upsert', { voter_id: voterId, election_year: 2022, voted: false })
    await invoke('VoterTurnout.Upsert', { voter_id: voterId, election_year: 2023, voted: true })

    const res = await invoke('VoterTurnout.GetByVoter', { voterId })
    expect(res.success).toBe(true)
    expect(Array.isArray(res.data)).toBe(true)
    const years = res.data.map((r: any) => r.electionYear ?? r.election_year)
    expect(years).toEqual([2022, 2023, 2024])
  })

  it('VoterTurnout.Delete soft-deletes and subsequent GetByVoter excludes it', async () => {
    const voterId = 'v4'
    const a = await invoke('VoterTurnout.Upsert', { voter_id: voterId, election_year: 2020, voted: true })
    const b = await invoke('VoterTurnout.Upsert', { voter_id: voterId, election_year: 2021, voted: false })
    expect(a.success).toBe(true)
    expect(a.data.id).toBeTruthy()
    expect(b.success).toBe(true)
    expect(b.data.id).toBeTruthy()

    const deleteRes = await invoke('VoterTurnout.Delete', { id: a.data.id })
    expect(deleteRes.success).toBe(true)

    const listRes = await invoke('VoterTurnout.GetByVoter', { voterId })
    expect(listRes.success).toBe(true)
    const years = listRes.data.map((r: any) => r.electionYear ?? r.election_year)
    expect(years).toEqual([2021])
  })

  it('VoterTurnout.Upsert validates election_year (reject year too far future)', async () => {
    const tooFuture = new Date().getFullYear() + 5
    const res = await invoke('VoterTurnout.Upsert', { voter_id: 'v5', election_year: tooFuture, voted: true })
    expect(res.success).toBe(false)
    expect(res.error).toBeTruthy()
    expect(res.error.code).toBe('VALIDATION_ERROR')
    // Should contain validation error about the year
    const errorMsg = res.error.message || JSON.stringify(res.error.details)
    expect(errorMsg.includes('Year') || errorMsg.includes('year') || errorMsg.includes('election')).toBe(true)
  })
})