import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mirror the voter IPC test pattern by mocking 'electron' to capture ipcMain handlers
const handlers: Record<string, Function> = {}
vi.mock('electron', () => {
  return {
    ipcMain: {
      handle: (channel: string, handler: Function) => {
        handlers[channel] = handler
      },
    },
    // minimal BrowserWindow/export if electron/main.ts imports them; provide dummies
    app: {
      on: vi.fn(),
      whenReady: vi.fn(() => Promise.resolve()),
    },
    BrowserWindow: vi.fn(),
  }
})

// Helper to call a registered handler like ipc<PERSON><PERSON><PERSON>.invoke would do
async function invoke(channel: string, payload: any) {
  const handler = handlers[channel]
  if (!handler) throw new Error(`No handler registered for channel: ${channel}`)
  // electron's ipcMain.handle signature: (event, ...args) handler(event, ...args)
  // Our safeHandle wraps to accept a single payload argument after event.
  return handler({} as any, payload)
}

describe('User IPC handlers', () => {
  beforeEach(async () => {
    // Clear prior handlers and dynamically import main to register fresh handlers
    for (const k of Object.keys(handlers)) delete handlers[k]
    // Re-import electron/main.ts to re-register handlers
    const modPath = '../../electron/main'
    // Purge module from cache to allow re-execution
    await vi.resetModules()
    await import(modPath)
  })

  it('login success returns user and token', async () => {
    const res = await invoke('User.Login', { username: 'owner', password: 'ownerpass' })
    expect(res.success).toBe(true)
    expect(res.data).toBeTruthy()
    expect(res.data.user).toBeTruthy()
    expect(res.data.user.username).toBe('owner')
    expect(res.data.token).toBeTypeOf('string')
    expect(res.data.token.length).toBeGreaterThan(0)
  })

  it('login failure returns INVALID_CREDENTIALS error mapping', async () => {
    const res = await invoke('User.Login', { username: 'owner', password: 'wrong' })
    expect(res.success).toBe(false)
    expect(res.error).toBeTruthy()
    expect(res.error.code).toBe('INVALID_CREDENTIALS')
    expect(res.error.message).toContain('Invalid username or password')
  })

  it('getById not found maps to NOT_FOUND', async () => {
    const res = await invoke('User.GetById', { id: 'does-not-exist' })
    expect(res.success).toBe(false)
    expect(res.error).toBeTruthy()
    expect(res.error.code).toBe('NOT_FOUND')
    expect(res.error.message).toContain('User not found')
  })

  it('upsert create success and then update username without conflict', async () => {
    const res = await invoke('User.Upsert', {
      username: 'alice',
      password: 'password123',
      role: 'editor',
      full_name: 'Alice A',
      active: true,
    })
    expect(res.success).toBe(true)
    expect(res.data.id).toBeTruthy()
    expect(res.data.username).toBe('alice')
    expect(res.data).not.toHaveProperty('password')

    // update
    const updated = await invoke('User.Upsert', {
      id: res.data.id,
      username: 'alice2',
      full_name: 'Alice A2',
    })
    expect(updated.success).toBe(true)
    expect(updated.data.id).toBe(res.data.id)
    expect(updated.data.username).toBe('alice2')
    expect(updated.data.fullName).toBe('Alice A2')
  })

  it('upsert create conflict on duplicate username', async () => {
    // First create bob
    const bob = await invoke('User.Upsert', {
      username: 'bob',
      password: 'password123',
      role: 'viewer',
    })
    expect(bob.success).toBe(true)
    expect(bob.data.username).toBe('bob')

    // Attempt to create another with same username
    const conflict = await invoke('User.Upsert', {
      username: 'bob',
      password: 'password456',
      role: 'viewer',
    })
    expect(conflict.success).toBe(false)
    expect(conflict.error).toBeTruthy()
    expect(conflict.error.code).toBe('CONFLICT')
    expect(conflict.error.message).toContain('Username already exists')
  })

  it('delete success then getById returns NOT_FOUND', async () => {
    const u = await invoke('User.Upsert', {
      username: 'tempuser',
      password: 'password123',
      role: 'viewer',
    })
    expect(u.success).toBe(true)

    const deleteRes = await invoke('User.Delete', { id: u.data.id })
    expect(deleteRes.success).toBe(true)

    const getRes = await invoke('User.GetById', { id: u.data.id })
    expect(getRes.success).toBe(false)
    expect(getRes.error.code).toBe('NOT_FOUND')
    expect(getRes.error.message).toContain('User not found')
  })

  it('logout returns null', async () => {
    const res = await invoke('User.Logout', {})
    expect(res.success).toBe(true)
    expect(res.data).toBeNull()
  })
})