import { describe, it, expect, beforeAll } from 'vitest'

// These globals are provided by electron/main.ts test bootstrap
declare global {
  // eslint-disable-next-line no-var
  var __ipcInvoke__: (channel: string, payload?: any) => Promise<any>
}

// Basic harness to ensure main module is loaded which registers handlers
async function bootstrapMain() {
  // Importing electron/main.ts sets up ipcMain mock and registers handlers
  await import('../../electron/main')
}

describe('Export IPC', () => {
  beforeAll(async () => {
    await bootstrapMain()
  })

  it('Export.Start should create a CSV export job and return jobId', async () => {
    const res = await globalThis.__ipcInvoke__('Export.Start', {
      type: 'csv',
      anonymize: true,
      filters: null,
    })

    // Plain channel returns data directly, not envelope
    expect(res).toBeTruthy()
    expect(typeof res.jobId).toBe('string')
    expect(res.jobId.length).toBeGreaterThan(0)

    // Immediately check status; job may still be running but should exist
    const status = await globalThis.__ipcInvoke__('Export.Status', { id: res.jobId })
    expect(status).toBeTruthy()
    expect(status.id).toBe(res.jobId)
    expect(['pending', 'running', 'completed', 'failed', 'canceled']).toContain(status.status)
    expect(typeof status.progress).toBe('number')
    // DTO fields from main.ts mapping
    expect(status).toHaveProperty('resultPath')
    expect(status).toHaveProperty('errorReportPath')
    expect(status).toHaveProperty('createdAt')
    expect(status).toHaveProperty('updatedAt')
  })

  it('Export.Start should validate type and throw on invalid type', async () => {
    await expect(
      globalThis.__ipcInvoke__('Export.Start', {
        type: 'invalid',
      })
    ).rejects.toThrowError(/VALIDATION_ERROR: type must be "csv" or "pdf"/)
  })

  it('Export.Status should throw NOT_FOUND for unknown job id', async () => {
    await expect(globalThis.__ipcInvoke__('Export.Status', { id: 'non-existent-job' })).rejects.toThrowError(
      /NOT_FOUND: export job|NOT_FOUND: job/
    )
  })

  it('Export.Cancel should validate id and cancel existing job', async () => {
    // Start a job first
    const { jobId } = await globalThis.__ipcInvoke__('Export.Start', { type: 'csv' })
    expect(jobId).toBeTruthy()

    // Cancel it
    const cancelRes = await globalThis.__ipcInvoke__('Export.Cancel', { id: jobId })
    // Plain channel returns null on success
    expect(cancelRes).toBeNull()

    // Status should reflect canceled (or completed if it raced to finish; allow either)
    const status = await globalThis.__ipcInvoke__('Export.Status', { id: jobId })
    expect(status.id).toBe(jobId)
    expect(['canceled', 'completed', 'failed', 'running', 'pending']).toContain(status.status)
  })

  it('Export.Cancel should throw NOT_FOUND for unknown id', async () => {
    await expect(globalThis.__ipcInvoke__('Export.Cancel', { id: 'unknown-id' })).rejects.toThrowError(/NOT_FOUND: job/)
  })
})