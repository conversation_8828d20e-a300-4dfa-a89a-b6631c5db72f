import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock electron to provide ipcMain with handle/on maps for tests
vi.mock('electron', () => {
  const handlers = new Map<string, Function>()
  return {
    ipcMain: {
      handle: (channel: string, fn: Function) => {
        handlers.set(channel, fn)
      },
      // Helper for tests to access registered handlers
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      __getHandler: (channel: string): any => handlers.get(channel),
      // no-op placeholders for listeners used elsewhere
      on: () => {},
      removeListener: () => {},
    },
    app: {
      getVersion: () => '0.0.0-test',
      on: () => {},
      whenReady: () => ({ then: (cb: any) => cb() }),
      quit: () => {},
    },
    BrowserWindow: class {
      static getAllWindows() { return [] }
      webContents = { on: () => {}, send: () => {} }
      loadURL() {}
      loadFile() {}
      isMaximized() { return false }
      minimize() {}
      maximize() {}
      unmaximize() {}
      close() {}
      constructor(_opts?: any) {}
    },
  }
})

// Import after mocking electron so main registers handlers against the mock ipcMain
// eslint-disable-next-line @typescript-eslint/no-unused-vars
let electronMain: any

async function invokeIpc<Req, Res>(channel: string, request: Req): Promise<Res> {
  const { ipcMain } = await import('electron')
  // @ts-expect-error internal helper exposed by our mock
  const handler = ipcMain.__getHandler(channel)
  if (!handler) throw new Error(`No ipcMain handler registered for channel: ${channel}`)
  return await handler({}, request)
}

describe('IPC: Voter channels', () => {
  beforeEach(async () => {
    vi.resetModules()
    // Re-import main so that handlers are (re)registered on the mocked ipcMain
    electronMain = await import('../../electron/main')
  })

  it('Voter.Search validates payload and returns structured success', async () => {
    const req = { query: 'John', filters: { status: 'Active' }, limit: 20, offset: 0 }
    const res: any = await invokeIpc('Voter.Search', req)
    expect(res).toMatchObject({ success: true, error: null })
    expect(res.data).toHaveProperty('voters')
    expect(res.data).toHaveProperty('total')
    expect(res.data).toHaveProperty('page')
    expect(res.data).toHaveProperty('limit')
    expect(res.data).toHaveProperty('hasMore')
  })

  it('Voter.Search rejects invalid payload via Zod', async () => {
    const badReq = { query: '', limit: -1 }
    const res: any = await invokeIpc('Voter.Search', badReq as any)
    expect(res.success).toBe(false)
    expect(res.error).toBeTruthy()
    expect(res.error).toHaveProperty('code')
    expect(res.error).toHaveProperty('message')
    expect(res.error).toHaveProperty('timestamp')
  })

  it('Voter.GetById validates payload and maps NOT_FOUND as error', async () => {
    const req = { id: 'missing' }
    const res: any = await invokeIpc('Voter.GetById', req)
    if (res.success === false) {
      expect(res.error).toMatchObject({
        code: expect.any(String),
        message: expect.any(String),
      })
    } else {
      expect('data' in res).toBe(true)
    }
  })

  it('Voter.Upsert (create) validates and returns created voter', async () => {
    const payload = {
      name: 'John Doe',
      relationship_type: 'Father',
      relationship_name: 'Robert',
      gender: 'Male',
      epic_number: '**********',
      house_number: '123',
      polling_station_id: 'ps-1',
      status: 'Active',
    }
    const res: any = await invokeIpc('Voter.Upsert', payload as any)
    if (res.success) {
      expect(res.data).toBeTruthy()
      expect(res.data).toHaveProperty('id')
      expect(res.data).toHaveProperty('created_at')
      expect(res.data).toHaveProperty('updated_at')
    } else {
      // Valid payload should generally succeed; keep assertion meaningful if not
      expect(res.error).toBeFalsy()
    }
  })

  it('Voter.Upsert rejects invalid EPIC via validation', async () => {
    const payload = {
      name: 'John Doe',
      relationship_type: 'Father',
      relationship_name: 'Robert',
      gender: 'Male',
      epic_number: 'INVALID123',
      house_number: '123',
      polling_station_id: 'ps-1',
      status: 'Active',
    }
    const res: any = await invokeIpc('Voter.Upsert', payload as any)
    expect(res.success).toBe(false)
    expect(res.error).toBeTruthy()
    expect(res.error.code).toBeDefined()
  })

  it('Voter.Delete validates request and returns success (or mapped error if not found)', async () => {
    const res: any = await invokeIpc('Voter.Delete', { id: 'some-id' })
    // Delete may fail with NOT_FOUND depending on repository behavior in main composition.
    // Accept either success or NOT_FOUND error shape to keep deterministic without DB.
    if (res.success) {
      expect(res.data).toBeNull()
    } else {
      expect(res.error).toBeTruthy()
      expect(res.error).toHaveProperty('code')
      expect(res.error).toHaveProperty('message')
    }
  })
})