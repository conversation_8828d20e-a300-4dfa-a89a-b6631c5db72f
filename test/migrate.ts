/**
 * Test migration helper: runs Drizzle migrations programmatically
 * Use this in tests BEFORE importing electron/main to ensure schema is up to date.
 *
 * Example usage in a test suite:
 *   import { beforeAll } from 'vitest'
 *   beforeAll(async () => {
 *     const { runMigrations } = await import('../test/migrate')
 *     await runMigrations()
 *     await import('../electron/main')
 *   })
 */

import fs from 'node:fs'
import path from 'node:path'
import { sqlite } from '../src/main/database/client'

/**
 * Execute a SQL file against the shared sqlite db used by the app.
 * Splits on semicolons while preserving basic statements.
 */
async function execSqlFile(filePath: string) {
  const sqlRaw = fs.readFileSync(filePath, 'utf8')

  // Remove SQL line comments
  const withoutComments = sqlRaw
    .split('\n')
    .map((line) => line.replace(/--.*$/g, ''))
    .join('\n')

  // Preserve multi-line statements like triggers by detecting CREATE TRIGGER blocks
  const lines = withoutComments.split('\n')

  const statements: string[] = []
  let buffer: string[] = []
  let inTrigger = false

  const flushBuffer = () => {
    const stmt = buffer.join('\n').trim()
    if (stmt) statements.push(stmt)
    buffer = []
  }

  for (let raw of lines) {
    const line = raw.trim()
    if (!line) continue

    // Detect start of trigger (or end of trigger at 'END;')
    if (/^CREATE\s+TRIGGER/i.test(line)) {
      inTrigger = true
    }

    buffer.push(line)

    if (inTrigger) {
      // A trigger body ends with END; possibly with spaces before ;
      if (/END\s*;?$/i.test(line)) {
        inTrigger = false
        flushBuffer()
      }
      continue
    }

    // Regular statements end with semicolon
    if (line.endsWith(';')) {
      flushBuffer()
    }
  }
  // Flush any remaining buffer (may be missing trailing semicolon)
  flushBuffer()

  for (const stmt of statements) {
    const trimmed = stmt.replace(/;+\s*$/g, '')
    if (!trimmed) continue
    try {
      sqlite.prepare(trimmed).run()
    } catch (err) {
      const msg = String(err ?? '')
      if (
        /already exists/i.test(msg) ||
        /duplicate/i.test(msg) ||
        /UNIQUE constraint failed/i.test(msg) ||
        /no such table/i.test(msg) ||
        /cannot commit - no transaction is active/i.test(msg)
      ) {
        continue
      }
      throw err
    }
  }
}

/**
 * Run all SQL migrations in the drizzle/migrations directory in lexical order.
 * This is intentionally simple and idempotent for test environments.
 */
export async function runMigrations(migrationsDir = path.resolve(process.cwd(), 'drizzle', 'migrations')) {
  if (!fs.existsSync(migrationsDir)) {
    // Nothing to migrate in environments missing migrations
    return
  }

  const entries = fs
    .readdirSync(migrationsDir, { withFileTypes: true })
    .filter((e) => e.isFile() && e.name.endsWith('.sql'))
    .map((e) => e.name)
    .sort()

  for (const name of entries) {
    const full = path.join(migrationsDir, name)
    await execSqlFile(full)
  }
}

// Optional convenience: migrate + verify key tables exist (lightweight check)
export async function ensureCoreSchema() {
  await runMigrations()
  // Basic check: export_jobs table should exist after migrations
  try {
    // Best-effort probe; ignore typing of returned row
    sqlite.prepare('SELECT 1 FROM export_jobs LIMIT 1').get()
  } catch {
    // ignore; existence will be enforced by subsequent SQL usage paths
  }
}