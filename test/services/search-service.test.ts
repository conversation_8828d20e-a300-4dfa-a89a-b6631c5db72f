/**
 * Unit tests for SearchService
 * Ensures delegation to VoterRepository with validation and pagination controls
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SearchService } from '@/main/services/search-service';
import { DomainError } from '@/shared/types/errors';

describe('SearchService', () => {
  let repo: any;
  let service: SearchService;

  beforeEach(() => {
    repo = {
      search: vi.fn(),
      findMany: vi.fn(),
      countWithFilters: vi.fn(),
    };
    service = new SearchService(repo);
  });

  describe('search', () => {
    it('delegates to repo.search with query and filters', async () => {
      const results = [{ id: 'v1', name: '<PERSON>' }];
      repo.search.mockResolvedValue(results);

      const query = 'john';
      const filters = { polling_station_id: 'ps1', status: 'Active' };

      const out = await service.search(query, filters);
      expect(out).toEqual(results);
      expect(repo.search).toHaveBeenCalledWith(query, filters);
    });

    it('validates non-empty query', async () => {
      await expect(service.search('')).rejects.toThrow(DomainError);
      await expect(service.search('   ')).rejects.toThrow(DomainError);
      expect(repo.search).not.toHaveBeenCalled();
    });

    it('enforces pagination limits on filters', async () => {
      repo.search.mockResolvedValue([]);
      await service.search('john', { limit: 9999, offset: -10 } as any);

      // Expect sanitized filters: limit clamped to 100, offset to 0
      expect(repo.search).toHaveBeenCalledWith('john', { limit: 100, offset: 0 });
    });
  });

  describe('list', () => {
    it('delegates to repo.findMany with filters', async () => {
      const rows = [{ id: '1' }];
      repo.findMany.mockResolvedValue(rows);

      const filters = { status: 'Active' };
      const out = await service.list(filters);

      expect(out).toEqual(rows);
      expect(repo.findMany).toHaveBeenCalledWith(filters);
    });

    it('enforces pagination defaults and limits', async () => {
      repo.findMany.mockResolvedValue([]);
      await service.list({ limit: 5000, offset: -1 } as any);

      expect(repo.findMany).toHaveBeenCalledWith({ limit: 100, offset: 0 });
    });
  });

  describe('count', () => {
    it('delegates to repo.countWithFilters', async () => {
      repo.countWithFilters.mockResolvedValue(42);
      const n = await service.count({ status: 'Active' });
      expect(n).toBe(42);
      expect(repo.countWithFilters).toHaveBeenCalledWith({ status: 'Active' });
    });
  });
});