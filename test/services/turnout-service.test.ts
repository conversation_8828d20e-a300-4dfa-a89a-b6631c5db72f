import { describe, it, expect, beforeEach, vi } from 'vitest'
import { TurnoutService, type TurnoutRepository, type VoterRepositoryForTurnout, type TurnoutRecord } from '../../src/main/services/turnout-service'
import { DomainError } from '../../src/shared/types/errors'

// Use Vitest's vi.fn mocks and cast to interface types using any (avoid generics on vi.fn due to TS signature)
function makeRepoMocks() {
  const turnoutRepo: TurnoutRepository & Record<string, any> = {
    findById: vi.fn(),
    find: vi.fn(),
    upsert: vi.fn(),
    delete: vi.fn(),
    listByVoter: vi.fn(),
    aggregateByStation: vi.fn(),
    aggregateBySection: vi.fn(),
    aggregateByDemographic: vi.fn(),
  } as any

  const voterRepo: VoterRepositoryForTurnout & Record<string, any> = {
    findById: vi.fn(),
  } as any

  return { turnoutRepo, voterRepo }
}

describe('TurnoutService', () => {
  let svc: TurnoutService
  let turnoutRepo: TurnoutRepository & Record<string, any>
  let voterRepo: VoterRepositoryForTurnout & Record<string, any>

  beforeEach(() => {
    const mocks = makeRepoMocks()
    turnoutRepo = mocks.turnoutRepo
    voterRepo = mocks.voterRepo
    svc = new TurnoutService(turnoutRepo, voterRepo)
  })

  describe('recordTurnout', () => {
    it('creates new record when none exists (upsert path)', async () => {
      ;(voterRepo.findById as any).mockResolvedValue({
        id: 'v1',
        polling_station_id: 'ps1',
        section_id: 's1',
        status: 'Active',
        deleted_at: null,
      } as any)

      const nowIso = new Date().toISOString()
      ;(turnoutRepo.upsert as any).mockResolvedValue({
        id: 't1',
        voterId: 'v1',
        electionYear: 2024,
        voted: true,
        notes: 'Arrived early',
        createdAt: nowIso,
        updatedAt: nowIso,
        deletedAt: null,
      } as unknown as TurnoutRecord)

      const res = await svc.recordTurnout({ voterId: 'v1', electionYear: 2024, voted: true, notes: 'Arrived early' })
      expect(res).toMatchObject({
        id: 't1',
        voterId: 'v1',
        electionYear: 2024,
        voted: true,
        notes: 'Arrived early',
      })
      expect(turnoutRepo.upsert).toHaveBeenCalledWith({
        voterId: 'v1',
        electionYear: 2024,
        voted: true,
        notes: 'Arrived early',
      })
    })

    it('validates voter existence and not soft-deleted', async () => {
      ;(voterRepo.findById as any).mockResolvedValue(null as any)
      await expect(svc.recordTurnout({ voterId: 'missing', electionYear: 2024, voted: false })).rejects.toThrow(
        DomainError
      )
      try {
        await svc.recordTurnout({ voterId: 'missing', electionYear: 2024, voted: false })
      } catch (e: any) {
        expect(String(e.message)).toMatch(/NOT_FOUND|Voter not found/)
      }

      ;(voterRepo.findById as any).mockResolvedValue({ id: 'v1', deleted_at: '2024-01-01T00:00:00.000Z' } as any)
      await expect(svc.recordTurnout({ voterId: 'v1', electionYear: 2024, voted: false })).rejects.toThrow(DomainError)
    })

    it('rejects invalid election year and long notes', async () => {
      ;(voterRepo.findById as any).mockResolvedValue({
        id: 'v1',
        polling_station_id: 'ps1',
        section_id: null,
        status: 'Active',
        deleted_at: null,
      } as any)

      await expect(svc.recordTurnout({ voterId: 'v1', electionYear: 1999, voted: true })).rejects.toThrow(DomainError)
      const longNotes = 'x'.repeat(1001)
      await expect(
        svc.recordTurnout({ voterId: 'v1', electionYear: 2024, voted: true, notes: longNotes })
      ).rejects.toThrow(DomainError)
    })
  })

  describe('getTurnoutByVoterYear', () => {
    it('returns null when not found', async () => {
      ;(turnoutRepo.find as any).mockResolvedValue(null as any)
      const res = await svc.getTurnoutByVoterYear('v1', 2024)
      expect(res).toBeNull()
    })

    it('returns DTO when found', async () => {
      const nowIso = new Date().toISOString()
      ;(turnoutRepo.find as any).mockResolvedValue({
        id: 't1',
        voterId: 'v1',
        electionYear: 2024,
        voted: true,
        notes: null,
        createdAt: nowIso,
        updatedAt: nowIso,
        deletedAt: null,
      })
      const res = await svc.getTurnoutByVoterYear('v1', 2024)
      expect(res).toMatchObject({ id: 't1', voterId: 'v1', electionYear: 2024, voted: true })
    })

    it('validates inputs', async () => {
      await expect(svc.getTurnoutByVoterYear('', 2024)).rejects.toThrow(DomainError)
      await expect(svc.getTurnoutByVoterYear('v1', 1999)).rejects.toThrow(DomainError)
    })
  })

  describe('getVoterTurnoutHistory', () => {
    it('lists turnout records by voter', async () => {
      const nowIso = new Date().toISOString()
      ;(turnoutRepo.listByVoter as any).mockResolvedValue([
        {
          id: 't1',
          voterId: 'v1',
          electionYear: 2019,
          voted: true,
          createdAt: nowIso,
          updatedAt: nowIso,
          deletedAt: null,
        },
        {
          id: 't2',
          voterId: 'v1',
          electionYear: 2024,
          voted: false,
          createdAt: nowIso,
          updatedAt: nowIso,
          deletedAt: null,
        },
      ] as any)

      const res = await svc.getVoterTurnoutHistory('v1')
      expect(res.length).toBe(2)
      expect(res[0]).toMatchObject({ voterId: 'v1', electionYear: 2019, voted: true })
    })

    it('validates voterId', async () => {
      await expect(svc.getVoterTurnoutHistory('')).rejects.toThrow(DomainError)
    })
  })

  describe('deleteTurnout', () => {
    it('throws NOT_FOUND when record missing', async () => {
      ;(turnoutRepo.findById as any).mockResolvedValue(null as any)
      await expect(svc.deleteTurnout('nope')).rejects.toThrow(DomainError)
      try {
        await svc.deleteTurnout('nope')
      } catch (e: any) {
        expect(String(e.message)).toMatch(/NOT_FOUND|Turnout record not found/)
      }
    })

    it('soft deletes when exists', async () => {
      const nowIso = new Date().toISOString()
      ;(turnoutRepo.findById as any).mockResolvedValue({
        id: 't1',
        voterId: 'v1',
        electionYear: 2024,
        voted: true,
        createdAt: nowIso,
        updatedAt: nowIso,
        deletedAt: null,
      } as any)
      ;(turnoutRepo.delete as any).mockResolvedValue(undefined as any)

      await svc.deleteTurnout('t1')
      expect(turnoutRepo.delete).toHaveBeenCalledWith('t1')
    })
  })

  describe('reports', () => {
    it('station report computes turnoutPct', async () => {
      ;(turnoutRepo.aggregateByStation as any).mockResolvedValue([
        { pollingStationId: 'ps1', total: 10, voted: 7 },
        { pollingStationId: 'ps2', total: 0, voted: 0 },
      ])
      const res = await svc.getStationReport(2024)
      expect(res[0]).toMatchObject({ pollingStationId: 'ps1', turnoutPct: 70 })
      expect(res[1]).toMatchObject({ pollingStationId: 'ps2', turnoutPct: 0 })
    })

    it('section report computes turnoutPct', async () => {
      ;(turnoutRepo.aggregateBySection as any).mockResolvedValue([
        { sectionId: 's1', total: 4, voted: 1 },
        { sectionId: null, total: 6, voted: 3 },
      ])
      const res = await svc.getSectionReport(2024, 'ps1')
      expect(res[0]).toMatchObject({ sectionId: 's1', turnoutPct: 25 })
      expect(res[1]).toMatchObject({ sectionId: null, turnoutPct: 50 })
    })

    it('demographic report computes turnoutPct', async () => {
      ;(turnoutRepo.aggregateByDemographic as any).mockResolvedValue([
        { key: 'Female', total: 8, voted: 6 },
        { key: 'Male', total: 12, voted: 6 },
      ])
      const res = await svc.getDemographicReport(2024, 'gender')
      expect(res[0]).toMatchObject({ key: 'Female', turnoutPct: 75 })
      expect(res[1]).toMatchObject({ key: 'Male', turnoutPct: 50 })
    })

    it('validates election year for reports', async () => {
      await expect(svc.getStationReport(1999)).rejects.toThrow(DomainError)
      await expect(svc.getSectionReport(1999)).rejects.toThrow(DomainError)
      await expect(svc.getDemographicReport(1999, 'gender')).rejects.toThrow(DomainError)
    })
  })
})