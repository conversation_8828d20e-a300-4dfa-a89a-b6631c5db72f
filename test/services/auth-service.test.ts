import { describe, it, expect, beforeEach } from "vitest";
import argon2 from "argon2";
import { DomainError, ErrorCode } from "@/shared/types/errors";
import type { z } from "zod";

// Minimal types and in-test doubles adhering to project naming rules
type UserRole = "owner" | "admin" | "editor" | "viewer";

type UserRecord = {
  id: string;
  username: string;
  role: UserRole;
  passwordHash: string;
  active: boolean;
  deletedAt: number | null;
  createdAt: number;
  updatedAt: number;
};

type UserDTO = {
  id: string;
  username: string;
  role: UserRole;
  active: boolean;
};

interface UserRepository {
  findById(id: string): Promise<UserRecord | null>;
  findByUsername(username: string, opts?: { includeDeleted?: boolean }): Promise<UserRecord | null>;
  create(data: { id: string; username: string; role: UserRole; passwordHash: string; active: boolean; createdAt: number; updatedAt: number; deletedAt: null }): Promise<UserRecord>;
  update(id: string, data: Partial<Pick<UserRecord, "username" | "role" | "passwordHash" | "active" | "deletedAt" | "updatedAt">>): Promise<UserRecord>;
}

function toDto(u: UserRecord): UserDTO {
  return { id: u.id, username: u.username, role: u.role, active: u.active };
}

// Simple ULID-ish generator for tests
function newId(): string {
  const now = Date.now().toString(36);
  const rand = Math.random().toString(36).slice(2, 10);
  return `${now}${rand}`.slice(0, 26);
}

class InMemoryUserRepository implements UserRepository {
  private items = new Map<string, UserRecord>();

  async findById(id: string): Promise<UserRecord | null> {
    return this.items.get(id) ?? null;
  }

  async findByUsername(username: string, opts?: { includeDeleted?: boolean }): Promise<UserRecord | null> {
    for (const u of this.items.values()) {
      if (u.username === username) {
        if (opts?.includeDeleted) return u;
        if (u.deletedAt == null) return u;
        return null;
      }
    }
    return null;
  }

  async create(data: { id: string; username: string; role: UserRole; passwordHash: string; active: boolean; createdAt: number; updatedAt: number; deletedAt: null }): Promise<UserRecord> {
    const rec: UserRecord = { ...data };
    this.items.set(rec.id, rec);
    return rec;
  }

  async update(id: string, data: Partial<Pick<UserRecord, "username" | "role" | "passwordHash" | "active" | "deletedAt" | "updatedAt">>): Promise<UserRecord> {
    const existing = this.items.get(id);
    if (!existing) {
      throw new DomainError("User not found", ErrorCode.NOT_FOUND);
    }
    const updated: UserRecord = { ...existing, ...data } as UserRecord;
    this.items.set(id, updated);
    return updated;
  }
}

// AuthService interface for tests
interface AuthService {
  createUser(input: { username: string; password: string; role?: UserRole }): Promise<UserDTO>;
  authenticate(username: string, password: string): Promise<UserDTO>;
  activate(userId: string): Promise<UserDTO>;
  deactivate(userId: string): Promise<UserDTO>;
  softDelete(userId: string): Promise<UserDTO>;
}

// Test-target: we will import real service later; for now define a minimal placeholder type import to satisfy path
// The actual implementation will live in src/main/services/auth-service.ts per instruction.
// Tests will import from "@/main/services/auth-service" once created.
// To keep red state meaningful, we reference the soon-to-exist path.
type AuthServiceCtor = new (repo: UserRepository) => AuthService;

describe("AuthService", () => {
  let repo: InMemoryUserRepository;

  // Lazy import to ensure red-green TDD: at first this import will fail until implementation exists.
  async function loadService(): Promise<AuthServiceCtor> {
    // Import using alias rule
    // eslint-disable-next-line @typescript-eslint/consistent-type-imports
    const mod = await import("@/main/services/auth-service");
    return (mod.AuthService as unknown) as AuthServiceCtor;
  }

  beforeEach(() => {
    repo = new InMemoryUserRepository();
  });

  it("creates a user with trimmed username, default role viewer, argon2-hashed password, and active=true", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    const dto = await service.createUser({ username: "  Alice  ", password: "abc12345" });
    expect(dto.username).toBe("Alice");
    expect(dto.role).toBe("viewer");
    expect(dto.active).toBe(true);
    expect(dto.id).toBeTruthy();

    const stored = await repo.findByUsername("Alice", { includeDeleted: true });
    expect(stored).toBeTruthy();
    expect(stored!.passwordHash).toBeTruthy();
    expect(stored!.passwordHash).not.toContain("abc12345");
    // verify argon2 hash valid
    const ok = await argon2.verify(stored!.passwordHash, "abc12345");
    expect(ok).toBe(true);
  });

  it("rejects invalid username (empty after trim)", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    await expect(service.createUser({ username: "   ", password: "abc12345" })).rejects.toMatchObject({
      code: ErrorCode.VALIDATION_ERROR,
    });
  });

  it("rejects invalid password policy (min 8, at least 1 letter and 1 number)", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    await expect(service.createUser({ username: "Bob", password: "short1" })).rejects.toMatchObject({
      code: ErrorCode.VALIDATION_ERROR,
    });
    await expect(service.createUser({ username: "Bob", password: "allletters" })).rejects.toMatchObject({
      code: ErrorCode.VALIDATION_ERROR,
    });
    await expect(service.createUser({ username: "Bob", password: "12345678" })).rejects.toMatchObject({
      code: ErrorCode.VALIDATION_ERROR,
    });
  });

  it("rejects role outside allowed set", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    // @ts-expect-error invalid role
    await expect(service.createUser({ username: "Carol", password: "abc12345", role: "superuser" })).rejects.toMatchObject({
      code: ErrorCode.VALIDATION_ERROR,
    });
  });

  it("requires username uniqueness case-sensitive; soft-deleted users block reuse", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    await service.createUser({ username: "Dave", password: "abc12345", role: "admin" });
    await expect(service.createUser({ username: "Dave", password: "abc12345" })).rejects.toMatchObject({
      code: ErrorCode.CONFLICT,
    });

    // Soft delete the user
    const d1 = await repo.findByUsername("Dave", { includeDeleted: true });
    expect(d1).toBeTruthy();
    await repo.update(d1!.id, { deletedAt: Date.now(), active: false, updatedAt: Date.now() });

    // Even after soft-delete, reuse should be blocked
    await expect(service.createUser({ username: "Dave", password: "abc12345" })).rejects.toMatchObject({
      code: ErrorCode.CONFLICT,
    });
  });

  it("authenticate returns INVALID_CREDENTIALS for missing user or wrong password or soft-deleted", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    // missing
    await expect(service.authenticate("Eve", "abc12345")).rejects.toMatchObject({
      code: ErrorCode.INVALID_CREDENTIALS,
    });

    // create
    await service.createUser({ username: "Eve", password: "abc12345", role: "editor" });

    // wrong password
    await expect(service.authenticate("Eve", "wrong999")).rejects.toMatchObject({
      code: ErrorCode.INVALID_CREDENTIALS,
    });

    // soft-delete blocks auth
    const rec = await repo.findByUsername("Eve", { includeDeleted: true });
    expect(rec).toBeTruthy();
    await repo.update(rec!.id, { deletedAt: Date.now(), active: false, updatedAt: Date.now() });

    await expect(service.authenticate("Eve", "abc12345")).rejects.toMatchObject({
      code: ErrorCode.INVALID_CREDENTIALS,
    });
  });

  it("authenticate returns minimal DTO without passwordHash on success", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    const created = await service.createUser({ username: "Frank", password: "abc12345", role: "viewer" });
    const authed = await service.authenticate("Frank", "abc12345");
    expect(authed).toEqual({
      id: created.id,
      username: "Frank",
      role: "viewer",
      active: true,
    });
    // ensure password hash not leaked anywhere
    const stored = await repo.findByUsername("Frank", { includeDeleted: true });
    expect(stored).toBeTruthy();
    expect((authed as unknown as { passwordHash?: string }).passwordHash).toBeUndefined();
  });

  it("activate, deactivate, softDelete toggle flags and return minimal DTO", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    const created = await service.createUser({ username: "Grace", password: "abc12345", role: "viewer" });
    expect(created.active).toBe(true);

    const deactivated = await service.deactivate(created.id);
    expect(deactivated.active).toBe(false);

    const activated = await service.activate(created.id);
    expect(activated.active).toBe(true);

    const softDeleted = await service.softDelete(created.id);
    expect(softDeleted.active).toBe(false);

    // after soft-delete, authenticate should fail
    await expect(service.authenticate("Grace", "abc12345")).rejects.toMatchObject({
      code: ErrorCode.INVALID_CREDENTIALS,
    });
  });

  it("createUser with explicit valid role keeps that role", async () => {
    const AuthServiceImpl = await loadService();
    const service = new AuthServiceImpl(repo);

    const dto = await service.createUser({ username: "Heidi", password: "abc12345", role: "admin" });
    expect(dto.role).toBe("admin");
  });
});