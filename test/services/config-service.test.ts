import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DomainError, ErrorCode } from '@/shared/types/errors';
import { ConfigService } from '@/main/services/config-service';

function makeRepoMocks() {
  return {
    categories: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    values: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
    },
    helpers: {
      countValueUsages: vi.fn(),
    },
  };
}

describe('ConfigService', () => {
  let repo: ReturnType<typeof makeRepoMocks>;
  let service: ConfigService;

  beforeEach(() => {
    repo = makeRepoMocks();
    service = new ConfigService(repo as any);
  });

  describe('category operations', () => {
    it('creates a category with unique name (case-insensitive)', async () => {
      repo.categories.findMany.mockResolvedValueOnce([]); // no duplicates
      repo.categories.create.mockResolvedValueOnce({
        id: 'cat_1',
        name: 'Education',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      });
      const created = await service.createCategory('Education');
      expect(created.id).toBe('cat_1');
      expect(repo.categories.create).toHaveBeenCalled();
    });

    it('rejects empty category name', async () => {
      await expect(service.createCategory('  ')).rejects.toMatchObject({
        code: ErrorCode.VALIDATION_ERROR,
      });
    });

    it('rejects duplicate category name (case-insensitive)', async () => {
      repo.categories.findMany.mockResolvedValueOnce([{ id: 'x', name: 'Education' }]);
      await expect(service.createCategory('education')).rejects.toMatchObject({
        code: ErrorCode.CONFLICT,
      });
    });

    it('renames a category with uniqueness checks', async () => {
      repo.categories.findUnique.mockResolvedValueOnce({ id: 'cat_1', name: 'Old' });
      repo.categories.findMany
        .mockResolvedValueOnce([]); // no conflicting names
      repo.categories.update.mockResolvedValueOnce({ id: 'cat_1', name: 'New' });
      const updated = await service.renameCategory('cat_1', 'New');
      expect(updated.name).toBe('New');
    });

    it('listCategories returns categories with value counts', async () => {
      repo.categories.findMany.mockResolvedValueOnce([
        { id: 'c1', name: 'Education' },
        { id: 'c2', name: 'Occupation' },
      ]);
      repo.values.findMany
        .mockResolvedValueOnce([{ id: 'v1', active: true }, { id: 'v2', active: false }]) // for c1
        .mockResolvedValueOnce([{ id: 'v3', active: true }]); // for c2
      const rows = await service.listCategories();
      expect(rows).toEqual([
        { id: 'c1', name: 'Education', counts: { active: 1, inactive: 1, total: 2 } },
        { id: 'c2', name: 'Occupation', counts: { active: 1, inactive: 0, total: 1 } },
      ]);
    });
  });

  describe('value operations', () => {
    it('adds a value unique within category (case-insensitive) and auto orders', async () => {
      repo.categories.findUnique.mockResolvedValueOnce({ id: 'c1', name: 'Education' });
      repo.values.findMany.mockResolvedValueOnce([
        { id: 'v1', label: 'Graduate', display_order: 1, active: true },
      ]);
      repo.values.create.mockResolvedValueOnce({
        id: 'v2',
        category_id: 'c1',
        label: 'Postgraduate',
        display_order: 2,
        active: true,
      });
      const created = await service.addValue('c1', 'Postgraduate');
      expect(created.display_order).toBe(2);
      expect(created.active).toBe(true);
    });

    it('rejects duplicate value label within category', async () => {
      repo.categories.findUnique.mockResolvedValueOnce({ id: 'c1' });
      repo.values.findMany.mockResolvedValueOnce([{ id: 'v1', label: 'Graduate' }]);
      await expect(service.addValue('c1', 'graduate')).rejects.toMatchObject({
        code: ErrorCode.CONFLICT,
      });
    });

    it('listValues respects ordering and active-first if same order', async () => {
      repo.categories.findUnique.mockResolvedValueOnce({ id: 'c1' });
      repo.values.findMany.mockResolvedValueOnce([
        { id: 'v2', label: 'B', display_order: 2, active: true },
        { id: 'v1', label: 'A', display_order: 1, active: true },
        { id: 'v3', label: 'B-inactive', display_order: 2, active: false },
      ]);
      const list = await service.listValues('c1');
      expect(list.map(v => v.id)).toEqual(['v1', 'v2', 'v3']);
    });

    it('updateValue enforces uniqueness on rename and supports toggling active/order', async () => {
      repo.values.findUnique.mockResolvedValueOnce({
        id: 'v1',
        category_id: 'c1',
        label: 'Old',
        display_order: 1,
        active: true,
      });
      repo.values.findMany
        .mockResolvedValueOnce([]); // no duplicate with label 'New' in c1
      repo.values.update.mockResolvedValueOnce({
        id: 'v1',
        category_id: 'c1',
        label: 'New',
        display_order: 3,
        active: false,
      });
      const updated = await service.updateValue('v1', { label: 'New', display_order: 3, active: false });
      expect(updated.label).toBe('New');
      expect(updated.display_order).toBe(3);
      expect(updated.active).toBe(false);
    });

    it('removeValue performs soft delete, blocks hard delete when referenced', async () => {
      repo.values.findUnique
        .mockResolvedValueOnce({ id: 'v1', category_id: 'c1' });
      repo.helpers.countValueUsages.mockResolvedValueOnce(2);
      await expect(service.removeValue('v1', { hard: true })).rejects.toMatchObject({
        code: ErrorCode.CONFLICT,
      });

      // soft delete path
      repo.values.findUnique.mockResolvedValueOnce({ id: 'v2', category_id: 'c1' });
      repo.helpers.countValueUsages.mockResolvedValueOnce(0);
      repo.values.softDelete.mockResolvedValueOnce({ id: 'v2', deleted_at: new Date() });
      const res = await service.removeValue('v2'); // default soft
      expect(res.id).toBe('v2');
      expect(repo.values.softDelete).toHaveBeenCalledWith('v2');
    });

    it('deactivateValue sets active false', async () => {
      repo.values.findUnique.mockResolvedValueOnce({ id: 'v1', active: true });
      repo.values.update.mockResolvedValueOnce({ id: 'v1', active: false });
      const res = await service.deactivateValue('v1');
      expect(res.active).toBe(false);
    });
  });

  describe('pagination', () => {
    it('sanitizes pagination for listCategories', async () => {
      repo.categories.findMany.mockResolvedValueOnce([]);
      const rows = await service.listCategories({ limit: 999, offset: -10 } as any);
      expect(rows).toEqual([]);
      // internal assertions by behavior; no throw means sanitize applied
    });

    it('sanitizes pagination for listValues', async () => {
      repo.categories.findUnique.mockResolvedValueOnce({ id: 'c1' });
      repo.values.findMany.mockResolvedValueOnce([]);
      const rows = await service.listValues('c1', { limit: 0, offset: -2 } as any);
      expect(rows).toEqual([]);
    });
  });

  describe('guards and errors', () => {
    it('NOT_FOUND for missing category/value', async () => {
      repo.categories.findUnique.mockResolvedValueOnce(null);
      await expect(service.addValue('missing', 'X')).rejects.toMatchObject({
        code: ErrorCode.NOT_FOUND,
      });

      repo.values.findUnique.mockResolvedValueOnce(null);
      await expect(service.updateValue('missing', { label: 'Y' })).rejects.toMatchObject({
        code: ErrorCode.NOT_FOUND,
      });
    });

    it('VALIDATION_ERROR for empty labels and names', async () => {
      await expect(service.addValue('c1', '  ')).rejects.toMatchObject({
        code: ErrorCode.VALIDATION_ERROR,
      });
      await expect(service.renameCategory('c1', '')).rejects.toMatchObject({
        code: ErrorCode.VALIDATION_ERROR,
      });
    });
  });
});