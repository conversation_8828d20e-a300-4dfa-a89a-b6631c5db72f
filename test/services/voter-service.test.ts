/**
 * Unit tests for VoterService
 * Tests business logic, validation, and rules for voter management
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { VoterService } from '../../src/main/services/voter-service';
import { DomainError } from '../../src/shared/types/errors';

// Mock dependencies
const mockVoterRepository = {
  findById: vi.fn(),
  findMany: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  softDelete: vi.fn(),
  hardDelete: vi.fn(),
  findByEpicNumber: vi.fn(),
  findByPollingStation: vi.fn(),
  findBySection: vi.fn(),
  search: vi.fn(),
  findByStatus: vi.fn(),
  getStatistics: vi.fn(),
  count: vi.fn(),
} as any;

describe('VoterService', () => {
  let voterService: VoterService;

  beforeEach(() => {
    vi.clearAllMocks();
    voterService = new VoterService(mockVoterRepository);
  });

  describe('createVoter', () => {
    it('creates a valid voter', async () => {
      const voterData = {
        name: 'John Doe',
        relationship_type: 'Father' as const,
        relationship_name: 'Robert Doe',
        gender: 'Male' as const,
        epic_number: '**********',
        house_number: '123 Main St',
        polling_station_id: 'station-1',
        status: 'Active' as const,
      };

      const createdVoter = {
        ...voterData,
        id: 'voter-1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockVoterRepository.create.mockResolvedValue(createdVoter);
      mockVoterRepository.findByEpicNumber.mockResolvedValue(null);

      const result = await voterService.createVoter(voterData);

      expect(result).toEqual(createdVoter);
      expect(mockVoterRepository.findByEpicNumber).toHaveBeenCalledWith('**********');
      expect(mockVoterRepository.create).toHaveBeenCalled();
    });

    it('validates EPIC number format', async () => {
      const invalid = {
        name: 'John Doe',
        relationship_type: 'Father' as const,
        relationship_name: 'Robert Doe',
        gender: 'Male' as const,
        epic_number: 'INVALID123',
        house_number: '123 Main St',
        polling_station_id: 'station-1',
        status: 'Active' as const,
      };
      await expect(voterService.createVoter(invalid)).rejects.toThrow(DomainError);
      expect(mockVoterRepository.create).not.toHaveBeenCalled();
    });

    it('validates required fields', async () => {
      const incomplete = {
        name: 'John Doe',
        epic_number: '**********',
        house_number: '123 Main St',
        polling_station_id: 'station-1',
        status: 'Active' as const,
      };
      await expect(voterService.createVoter(incomplete as any)).rejects.toThrow(DomainError);
      expect(mockVoterRepository.create).not.toHaveBeenCalled();
    });

    it('validates birth year range', async () => {
      const currentYear = new Date().getFullYear();
      const invalidBirthYear = currentYear - 10;

      const payload = {
        name: 'John Doe',
        relationship_type: 'Father' as const,
        relationship_name: 'Robert Doe',
        gender: 'Male' as const,
        birth_year: invalidBirthYear,
        epic_number: '**********',
        house_number: '123 Main St',
        polling_station_id: 'station-1',
        status: 'Active' as const,
      };

      await expect(voterService.createVoter(payload as any)).rejects.toThrow(DomainError);
      expect(mockVoterRepository.create).not.toHaveBeenCalled();
    });

    it('rejects duplicate EPIC numbers', async () => {
      const payload = {
        name: 'John Doe',
        relationship_type: 'Father' as const,
        relationship_name: 'Robert Doe',
        gender: 'Male' as const,
        epic_number: '**********',
        house_number: '123 Main St',
        polling_station_id: 'station-1',
        status: 'Active' as const,
      };
      mockVoterRepository.findByEpicNumber.mockResolvedValue({ id: 'existing' });
      await expect(voterService.createVoter(payload as any)).rejects.toThrow(DomainError);
      expect(mockVoterRepository.create).not.toHaveBeenCalled();
    });

    it('wraps database errors', async () => {
      const payload = {
        name: 'John Doe',
        relationship_type: 'Father' as const,
        relationship_name: 'Robert Doe',
        gender: 'Male' as const,
        epic_number: '**********',
        house_number: '123 Main St',
        polling_station_id: 'station-1',
        status: 'Active' as const,
      };
      mockVoterRepository.findByEpicNumber.mockResolvedValue(null);
      mockVoterRepository.create.mockRejectedValue(new Error('db'));
      await expect(voterService.createVoter(payload as any)).rejects.toThrow(DomainError);
    });
  });

  describe('updateVoter', () => {
    it('updates with validation', async () => {
      const voterId = 'v1';
      const updateData = { name: 'Jane Doe', relationship_type: 'Mother' as const };
      const existing = {
        id: voterId,
        name: 'John Doe',
        relationship_type: 'Father' as const,
        relationship_name: 'Robert Doe',
        gender: 'Male' as const,
        epic_number: '**********',
        house_number: '123 Main',
        polling_station_id: 'station-1',
        status: 'Active' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      const updated = { ...existing, ...updateData };

      mockVoterRepository.findById.mockResolvedValue(existing);
      mockVoterRepository.findByEpicNumber.mockResolvedValue(null);
      mockVoterRepository.update.mockResolvedValue(updated);

      const result = await voterService.updateVoter(voterId, updateData as any);
      expect(result).toEqual(updated);
      expect(mockVoterRepository.update).toHaveBeenCalled();
    });

    it('validates epic on update', async () => {
      await expect(
        voterService.updateVoter('v1', { epic_number: 'INVALID123' } as any)
      ).rejects.toThrow(DomainError);
      expect(mockVoterRepository.update).not.toHaveBeenCalled();
    });

    it('prevents duplicate epic on update', async () => {
      const voterId = 'v1';
      const existing = { id: voterId };
      mockVoterRepository.findById.mockResolvedValue(existing);
      mockVoterRepository.findByEpicNumber.mockResolvedValue({ id: 'v2' });
      await expect(
        voterService.updateVoter(voterId, { epic_number: '**********' } as any)
      ).rejects.toThrow(DomainError);
      expect(mockVoterRepository.update).not.toHaveBeenCalled();
    });

    it('handles non-existent voter', async () => {
      mockVoterRepository.findById.mockResolvedValue(null);
      await expect(voterService.updateVoter('missing', { name: 'Jane' } as any)).rejects.toThrow(DomainError);
      expect(mockVoterRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('deleteVoter', () => {
    it('soft deletes voter', async () => {
      const voterId = 'v1';
      mockVoterRepository.findById.mockResolvedValue({ id: voterId, status: 'Active' });
      await voterService.deleteVoter(voterId);
      expect(mockVoterRepository.softDelete).toHaveBeenCalledWith(voterId);
    });

    it('auto soft deletes Duplicate/Disqualified', async () => {
      const voterId = 'v1';
      mockVoterRepository.findById.mockResolvedValue({ id: voterId, status: 'Duplicate' });
      await voterService.deleteVoter(voterId);
      expect(mockVoterRepository.softDelete).toHaveBeenCalledWith(voterId);
    });

    it('handles non-existent on delete', async () => {
      mockVoterRepository.findById.mockResolvedValue(null);
      await expect(voterService.deleteVoter('missing')).rejects.toThrow(DomainError);
      expect(mockVoterRepository.softDelete).not.toHaveBeenCalled();
    });
  });

  describe('getVoterStatistics', () => {
    it('returns stats', async () => {
      const stats = { total: 1, active: 1, expired: 0, shifted: 0, missing: 0, duplicate: 0, disqualified: 0 };
      mockVoterRepository.getStatistics.mockResolvedValue(stats);
      expect(await voterService.getVoterStatistics()).toEqual(stats);
    });
  });

  describe('searchVoters', () => {
    it('search with filters', async () => {
      const query = 'John';
      const filters = { pollingStationId: 'station-1', status: 'Active' };
      const results = [{ id: 'v1', name: 'John' }];
      mockVoterRepository.search.mockResolvedValue(results);
      expect(await voterService.searchVoters(query, filters as any)).toEqual(results);
      expect(mockVoterRepository.search).toHaveBeenCalledWith(query, filters as any);
    });

    it('search with query only', async () => {
      const query = 'John';
      const results = [{ id: 'v1', name: 'John' }];
      mockVoterRepository.search.mockResolvedValue(results);
      expect(await voterService.searchVoters(query)).toEqual(results);
      expect(mockVoterRepository.search).toHaveBeenCalledWith(query, undefined);
    });
  });

  describe('getVotersByPollingStation', () => {
    it('returns voters for polling station', async () => {
      const pollingStationId = 'station-1';
      const voters = [{ id: 'v1' }, { id: 'v2' }];
      mockVoterRepository.findByPollingStation.mockResolvedValue(voters);
      expect(await voterService.getVotersByPollingStation(pollingStationId)).toEqual(voters);
      expect(mockVoterRepository.findByPollingStation).toHaveBeenCalledWith(pollingStationId, undefined);
    });
  });

  describe('getVotersBySection', () => {
    it('returns voters for section', async () => {
      const sectionId = 'section-1';
      const voters = [{ id: 'v1' }];
      mockVoterRepository.findBySection.mockResolvedValue(voters);
      expect(await voterService.getVotersBySection(sectionId)).toEqual(voters);
      expect(mockVoterRepository.findBySection).toHaveBeenCalledWith(sectionId);
    });

    it('returns unassigned voters for "unassigned"', async () => {
      const sectionId = 'unassigned';
      const voters = [{ id: 'v1', section_id: null }];
      mockVoterRepository.findBySection.mockResolvedValue(voters);
      expect(await voterService.getVotersBySection(sectionId)).toEqual(voters);
      expect(mockVoterRepository.findBySection).toHaveBeenCalledWith(sectionId);
    });
  });
});