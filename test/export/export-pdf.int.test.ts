import { describe, it, beforeAll, expect } from 'vitest'
import fs from 'node:fs'
import path from 'node:path'
// Fallback to relative path to avoid path alias resolution issues in tests
import { runMigrations as runTestMigrations } from '../migrate'

// Important: ensure migrations run BEFORE importing electron/main.ts so __ipcInvoke__ exists
let ipcInvoke: (channel: string, payload?: any) => Promise<any>

describe('Export PDF Integration', () => {
  beforeAll(async () => {
    await runTestMigrations()
    // Import main AFTER migrations so it can initialize globals for IPC
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    require('@/electron/main')
    // injected by main.ts for tests
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore - provided by electron/main.ts during tests
    ipcInvoke = (globalThis as any).__ipcInvoke__
    expect(typeof ipcInvoke).toBe('function')
  }, 60_000)

  it('should complete a stub PDF export and write a .pdf file', async () => {
    // Start a PDF export
    const startRes = await ipcInvoke('Export.Start', {
      type: 'pdf',
      filters: null,
      anonymize: false,
    })
    expect(startRes).toBeTruthy()
    expect(typeof startRes.jobId).toBe('string')
    const jobId = startRes.jobId as string

    // Poll for completion
    const deadline = Date.now() + 30_000
    let last: any = null
    while (Date.now() < deadline) {
      last = await ipcInvoke('Export.Status', { id: jobId })
      if (last?.status === 'completed' || last?.status === 'failed' || last?.status === 'canceled') break
      await new Promise((r) => setTimeout(r, 200))
    }

    expect(last, 'status response should exist').toBeTruthy()
    expect(['completed', 'failed', 'canceled']).toContain(last.status)

    // Happy path: expect completed with 100% and a pdf resultPath
    expect(last.status).toBe('completed')
    expect(last.progress).toBe(100)
    expect(typeof last.resultPath).toBe('string')
    const resultPath = last.resultPath as string
    expect(resultPath.endsWith('.pdf')).toBe(true)

    // File should exist and start with %PDF
    expect(fs.existsSync(resultPath)).toBe(true)
    const fd = fs.openSync(resultPath, 'r')
    const buf = Buffer.alloc(5)
    fs.readSync(fd, buf, 0, 5, 0)
    fs.closeSync(fd)
    expect(buf.toString('utf8')).toBe('%PDF-')

    // Also ensure it lives under tmp/exports by default (runner default)
    const expectedDir = path.resolve(process.cwd(), 'tmp', 'exports')
    expect(resultPath.startsWith(expectedDir)).toBe(true)
  }, 60_000)
it('should generate a multi-page PDF when enough rows exist', async () => {
    // Start a PDF export with a filter that likely matches many rows
    const startRes = await ipcInvoke('Export.Start', {
      type: 'pdf',
      filters: { status: 'Active' }, // if filters are ignored by stub, this still exercises runner path
      anonymize: false,
    })
    expect(startRes).toBeTruthy()
    const jobId = startRes.jobId as string
    expect(typeof jobId).toBe('string')

    // Poll for completion (should reach completed once multi-page generation is implemented)
    const deadline = Date.now() + 60_000
    let last: any = null
    while (Date.now() < deadline) {
      last = await ipcInvoke('Export.Status', { id: jobId })
      if (last?.status === 'completed' || last?.status === 'failed' || last?.status === 'canceled') break
      await new Promise((r) => setTimeout(r, 200))
    }

    expect(last, 'status response should exist').toBeTruthy()
    expect(['completed', 'failed', 'canceled']).toContain(last.status)
    expect(last.status).toBe('completed')
    expect(last.progress).toBe(100)

    const resultPath = last.resultPath as string
    expect(typeof resultPath).toBe('string')
    expect(resultPath.endsWith('.pdf')).toBe(true)
    expect(fs.existsSync(resultPath)).toBe(true)

    const fd = fs.openSync(resultPath, 'r')
    const header = Buffer.alloc(5)
    fs.readSync(fd, header, 0, 5, 0)
    fs.closeSync(fd)
    expect(header.toString('utf8')).toBe('%PDF-')

    // Heuristic that multi-page output should be larger than tiny single-page stub.
    const stats = fs.statSync(resultPath)
    expect(stats.size).toBeGreaterThan(1024) // adjust threshold if needed as implementation matures
  }, 120_000)

  it('should allow cancel mid-generation and leave a partial PDF with non-100 progress', async () => {
    // Start a PDF export intended to span multiple pages
    const startRes = await ipcInvoke('Export.Start', {
      type: 'pdf',
      filters: { status: 'Active' },
      anonymize: false,
    })
    expect(startRes).toBeTruthy()
    const jobId = startRes.jobId as string
    expect(typeof jobId).toBe('string')

    // Wait until the job is running (or progresses beyond initial COUNT checkpoint),
    // then cancel. We poll briefly for a running state to minimize race conditions.
    const runningDeadline = Date.now() + 15_000
    let snapshot: any = null
    while (Date.now() < runningDeadline) {
      snapshot = await ipcInvoke('Export.Status', { id: jobId })
      // consider "running" if status is running or progress exceeded 0
      if (snapshot?.status === 'running' || (typeof snapshot?.progress === 'number' && snapshot.progress > 0)) break
      await new Promise((r) => setTimeout(r, 150))
    }

    // Issue cancel
    await ipcInvoke('Export.Cancel', { id: jobId })

    // Poll to terminal (canceled/failed/completed)
    const deadline = Date.now() + 60_000
    let last: any = null
    while (Date.now() < deadline) {
      last = await ipcInvoke('Export.Status', { id: jobId })
      if (last?.status === 'completed' || last?.status === 'failed' || last?.status === 'canceled') break
      await new Promise((r) => setTimeout(r, 200))
    }

    expect(last, 'status response should exist').toBeTruthy()
    expect(['completed', 'failed', 'canceled']).toContain(last.status)
    expect(last.status).toBe('canceled')

    // Progress should be less than 100 or match a checkpoint (e.g., 40 or 70)
    expect(typeof last.progress).toBe('number')
    expect(last.progress).toBeLessThan(100)

    // A partial PDF may already be present; if resultPath exists, validate header
    if (last.resultPath) {
      const resultPath = last.resultPath as string
      expect(resultPath.endsWith('.pdf')).toBe(true)
      expect(fs.existsSync(resultPath)).toBe(true)
      const fd = fs.openSync(resultPath, 'r')
      const header = Buffer.alloc(5)
      fs.readSync(fd, header, 0, 5, 0)
      fs.closeSync(fd)
      expect(header.toString('utf8')).toBe('%PDF-')
    }
  }, 120_000)
})