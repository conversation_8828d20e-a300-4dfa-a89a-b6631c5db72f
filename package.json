{"name": "electixir-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^6.1.0", "lucide-react": "^0.536.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "ulid": "^3.0.1", "zod": "^4.0.14", "zustand": "^4.5.7"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^24.1.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.2.4", "argon2": "^0.43.1", "better-sqlite3": "^12.2.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "ts-node": "^10.9.2", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "vitest": "^3.2.4"}, "main": "dist-electron/main.js"}