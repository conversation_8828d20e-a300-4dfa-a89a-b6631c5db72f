import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import { z } from 'zod';
import {
  ImportChannels,
  zImportStartRequest,
  zImportStartResponse,
  zImportProgressEvent,
  zImportCancelRequest,
  zImportCancelResponse,
  zImportResult,
} from '@/shared/types/ipc-import';
import { parseCsvStream, type Checkpoint } from '@/main/workers/import/csv-worker';

// Simple in-memory cancellations
const cancelFlags = new Map<string, { cancelled: boolean }>();

function publishProgress(target: BrowserWindow | null, progress: Checkpoint) {
  if (!target) return;
  const parsed = zImportProgressEvent.safeParse(progress);
  if (!parsed.success) return;
  target.webContents.send(ImportChannels.Progress, parsed.data);
}

export function registerImportIpc(getWindow: () => BrowserWindow | null) {
  // Start Import
  ipcMain.handle(ImportChannels.Start, async (_evt, raw: unknown) => {
    const win = getWindow();
    const req = zImportStartRequest.parse(raw);
    const startedAt = Date.now();

    // init cancel flag
    cancelFlags.set(req.importId, { cancelled: false });

    // fire-and-forget async run, respond accepted immediately
    (async () => {
      try {
        // periodic checkpoint hook
        const onCheckpoint = (cp: Checkpoint) => {
          // honour cancel flag
          const f = cancelFlags.get(req.importId);
          if (f?.cancelled) return;
          publishProgress(win, cp);
        };

        const result = await parseCsvStream(req.filePath, {
          importId: req.importId,
          checkpointEvery: req.checkpointEvery,
          onCheckpoint,
        });

        // final result event
        const final = zImportResult.parse({
          importId: result.importId,
          totalRows: result.totalRows,
          validRows: result.validRows,
          errorCount: result.errorCount,
          duplicateCount: result.duplicateCount,
          durationMs: result.durationMs,
        });
        win?.webContents.send(ImportChannels.Result, final);
      } catch (err) {
        // send a terminal result with zeros if parse failed catastrophically
        const fallback = zImportResult.parse({
          importId: req.importId,
          totalRows: 0,
          validRows: 0,
          errorCount: 0,
          duplicateCount: 0,
          durationMs: 0,
        });
        win?.webContents.send(ImportChannels.Result, fallback);
      } finally {
        cancelFlags.delete(req.importId);
      }
    })().catch(() => {
      // already handled above
    });

    const res = zImportStartResponse.parse({
      accepted: true,
      importId: req.importId,
      startedAt,
    });
    return res;
  });

  // Cancel Import
  ipcMain.handle(ImportChannels.Cancel, async (_evt, raw: unknown) => {
    const req = zImportCancelRequest.parse(raw);
    const entry = cancelFlags.get(req.importId);
    if (entry) entry.cancelled = true;
    const res = zImportCancelResponse.parse({
      importId: req.importId,
      cancelled: true,
      cancelledAt: Date.now(),
    });
    return res;
  });

  // Relay explicit Result fetch (optional future use)
  ipcMain.handle(ImportChannels.Result, async () => {
    // no-op: results are pushed via events; handler kept for symmetry if renderer invokes it
    return null;
  });
}