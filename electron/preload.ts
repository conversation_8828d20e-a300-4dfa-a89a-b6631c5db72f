/**
 * Secure preload script for Electron renderer process
 * Provides typed IPC communication with context isolation
 */

import { context<PERSON>ridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import {
  type IPCChannels,
  type ChannelRequest,
  type ChannelResponse,
  validateIPCRequest
} from '@/shared/types/ipc-channels';
import {
  ImportChannels,
  zImportStartRequest,
  zImportStartResponse,
  zImportProgressEvent,
  zImportCancelRequest,
  zImportCancelResponse,
  zImportResult,
  type ImportStartRequest,
  type ImportStartResponse,
  type ImportProgressEvent,
  type ImportCancelRequest,
  type ImportCancelResponse,
  type ImportResult,
} from '@/shared/types/ipc-import';

// Make sure Vite treats these as dependencies of the preload bundle (avoid unresolved alias warnings)
void ImportChannels;
void validateIPCRequest;

// Type-safe IPC handler wrapper
class IPCClient {
  private async sendRequest<T extends keyof IPCChannels>(
    channel: T,
    request: ChannelRequest<T>
  ): Promise<ChannelResponse<T>> {
    try {
      const response = await ipcRenderer.invoke(String(channel), request);
      return response;
    } catch (error) {
      console.error(`IPC Error in ${String(channel)}:`, error);
      throw error;
    }
  }

  // Voter operations
  async searchVoters(request: ChannelRequest<'Voter.Search'>): Promise<ChannelResponse<'Voter.Search'>> {
    validateIPCRequest('Voter.Search', request);
    return this.sendRequest('Voter.Search', request);
  }

  async getVoterById(request: ChannelRequest<'Voter.GetById'>): Promise<ChannelResponse<'Voter.GetById'>> {
    validateIPCRequest('Voter.GetById', request);
    return this.sendRequest('Voter.GetById', request);
  }

  async upsertVoter(request: ChannelRequest<'Voter.Upsert'>): Promise<ChannelResponse<'Voter.Upsert'>> {
    validateIPCRequest('Voter.Upsert', request);
    return this.sendRequest('Voter.Upsert', request);
  }

  // ConfigOption operations
  async configGetByCategory(request: ChannelRequest<'ConfigOption.GetByCategory'>): Promise<ChannelResponse<'ConfigOption.GetByCategory'>> {
    validateIPCRequest('ConfigOption.GetByCategory', request);
    return this.sendRequest('ConfigOption.GetByCategory', request);
  }

  async configUpsert(request: ChannelRequest<'ConfigOption.Upsert'>): Promise<ChannelResponse<'ConfigOption.Upsert'>> {
    validateIPCRequest('ConfigOption.Upsert', request);
    return this.sendRequest('ConfigOption.Upsert', request);
  }

  async configDelete(request: ChannelRequest<'ConfigOption.Delete'>): Promise<ChannelResponse<'ConfigOption.Delete'>> {
    validateIPCRequest('ConfigOption.Delete', request);
    return this.sendRequest('ConfigOption.Delete', request);
  }

  // Import operations
  async importStart(request: ImportStartRequest): Promise<ImportStartResponse> {
    const parsed = zImportStartRequest.parse(request);
    const resp = await ipcRenderer.invoke(ImportChannels.Start, parsed);
    return zImportStartResponse.parse(resp);
  }

  onImportProgress(callback: (event: ImportProgressEvent) => void): () => void {
    const handler = (_: unknown, payload: unknown) => {
      const ev = zImportProgressEvent.safeParse(payload);
      if (ev.success) callback(ev.data);
    };
    ipcRenderer.on(ImportChannels.Progress, handler as any);
    return () => ipcRenderer.removeListener(ImportChannels.Progress, handler as any);
  }

  onImportResult(callback: (result: ImportResult) => void): () => void {
    const handler = (_: unknown, payload: unknown) => {
      const res = zImportResult.safeParse(payload);
      if (res.success) callback(res.data);
    };
    ipcRenderer.on(ImportChannels.Result, handler as any);
    return () => ipcRenderer.removeListener(ImportChannels.Result, handler as any);
  }

  async importCancel(request: ImportCancelRequest): Promise<ImportCancelResponse> {
    const parsed = zImportCancelRequest.parse(request);
    const resp = await ipcRenderer.invoke(ImportChannels.Cancel, parsed);
    return zImportCancelResponse.parse(resp);
  }

  // Export operations
  async startExport(request: ChannelRequest<'Export.Start'>): Promise<ChannelResponse<'Export.Start'>> {
    validateIPCRequest('Export.Start', request);
    return this.sendRequest('Export.Start', request);
  }

  // User operations
  async login(request: ChannelRequest<'User.Login'>): Promise<ChannelResponse<'User.Login'>> {
    validateIPCRequest('User.Login', request);
    return this.sendRequest('User.Login', request);
  }

  async logout(request: ChannelRequest<'User.Logout'>): Promise<ChannelResponse<'User.Logout'>> {
    validateIPCRequest('User.Logout', request);
    return this.sendRequest('User.Logout', request);
  }

  // System operations
  async getSystemInfo(request: ChannelRequest<'System.Info'>): Promise<ChannelResponse<'System.Info'>> {
    validateIPCRequest('System.Info', request);
    return this.sendRequest('System.Info', request);
  }

  async getDatabaseStats(request: ChannelRequest<'Database.Stats'>): Promise<ChannelResponse<'Database.Stats'>> {
    validateIPCRequest('Database.Stats', request);
    return this.sendRequest('Database.Stats', request);
  }

  // Legacy example listeners retained (no-op for new import)
  onProgressUpdate(callback: (event: any, data: any) => void): () => void {
    ipcRenderer.on('progress-update', callback);
    return () => {
      ipcRenderer.removeListener('progress-update', callback);
    };
  }

  onJobStatusUpdate(callback: (event: any, data: any) => void): () => void {
    ipcRenderer.on('job-status-update', callback);
    return () => {
      ipcRenderer.removeListener('job-status-update', callback);
    };
  }

  onError(callback: (event: any, data: any) => void): () => void {
    ipcRenderer.on('error', callback);
    return () => {
      ipcRenderer.removeListener('error', callback);
    };
  }
}

// Expose a secure API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  ipc: new IPCClient(),
  // Back-compat shim for demos expecting window.electronAPI.ipc.searchVoters()
  // These delegate to the typed IPCClient methods above.
  // Note: keep minimal surface; replace demos to call ipc.* directly later.
  ipcLegacy: {
    searchVoters: (request: any) => (new IPCClient()).searchVoters(request),
    getVoterById: (request: any) => (new IPCClient()).getVoterById(request),
    upsertVoter: (request: any) => (new IPCClient()).upsertVoter(request),
    configGetByCategory: (request: any) => (new IPCClient()).configGetByCategory(request),
    configUpsert: (request: any) => (new IPCClient()).configUpsert(request),
    configDelete: (request: any) => (new IPCClient()).configDelete(request),
  },
  import: {
    start: async (req: ImportStartRequest) => {
      const parsedReq = zImportStartRequest.parse(req);
      const resp = await ipcRenderer.invoke(ImportChannels.Start, parsedReq);
      return zImportStartResponse.parse(resp);
    },
    onProgress: (cb: (ev: ImportProgressEvent) => void) => {
      const handler = (_: unknown, payload: unknown) => {
        const ev = zImportProgressEvent.safeParse(payload);
        if (ev.success) cb(ev.data);
      };
      ipcRenderer.on(ImportChannels.Progress, handler as any);
      return () => ipcRenderer.removeListener(ImportChannels.Progress, handler as any);
    },
    onResult: (cb: (res: ImportResult) => void) => {
      const handler = (_: unknown, payload: unknown) => {
        const res = zImportResult.safeParse(payload);
        if (res.success) cb(res.data);
      };
      ipcRenderer.on(ImportChannels.Result, handler as any);
      return () => ipcRenderer.removeListener(ImportChannels.Result, handler as any);
    },
    cancel: async (req: ImportCancelRequest) => {
      const parsed = zImportCancelRequest.parse(req);
      const resp = await ipcRenderer.invoke(ImportChannels.Cancel, parsed);
      return zImportCancelResponse.parse(resp);
    },
  },
});

// Type declarations for the window object
declare global {
  interface Window {
    electronAPI: {
      ipc: IPCClient;
      import: {
        start: (req: ImportStartRequest) => Promise<ImportStartResponse>;
        onProgress: (cb: (ev: ImportProgressEvent) => void) => () => void;
        onResult: (cb: (res: ImportResult) => void) => () => void;
        cancel: (req: ImportCancelRequest) => Promise<ImportCancelResponse>;
      };
    };
  }
}
