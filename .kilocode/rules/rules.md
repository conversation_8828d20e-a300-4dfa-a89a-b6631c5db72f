# Project Rules: Voter Management System

This document consolidates the core requirements and design intent from the voter management system docs to guide AI and contributors.

## Source Documents
- requirements: docs/specs/01_requirements.md
- design: docs/specs/02_design.md
- tasks: docs/specs/03_tasks.md

## Database Schema
- schema plan: docs/sample/database-schema-plan.md
- CSV sample: docs/sample/DB_SAMPLE.md

## Architectural Principles
- Offline-first Electron application with strict main/renderer separation.
- Security-first: contextIsolation enabled, nodeIntegration disabled in renderer, strict CSP, typed IPC, Zod validation at every boundary.
- Performance: SQLite WAL mode, strategic indexing, FTS5 for search, virtualization for large lists.
- Type safety end-to-end with TypeScript and Zod.
- Repository + Service layers, IPC as the only bridge to main from renderer.

## Functional Scope (from requirements)
1) Import CSV
- Validate CSV structure and show mapping UI.
- Required headers: name, relation_type, relation_name, house_number, birth_year, gender, epic_number, polling_station, section.
- Error report with row numbers; reject duplicate EPICs; summary on completion.

2) Search and filter
- Full-text search on name, epic, house number; latency target ≤ 300ms.
- Sidebar filters: polling station, section, and "Unassigned".
- Combine filters with real-time updates; virtualized rendering for 100k+ rows.

3) Voter management
- Validate required fields: name, polling_station_id.
- EPIC format ********** and unique among active.
- Birth year between 1900 and current_year - 18.
- Status enum: Active, Expired, Shifted, Missing, Duplicate, Disqualified.
- Duplicate/Disqualified auto soft-delete.
- Supporter and other categorical fields come from config_options.

4) Turnout tracking
- One record per voter per election year; voted/not voted; notes; reports by station/section/demographics.

5) Transactions
- Require voter, date, purpose (from config), amount (integer).
- History chronological; reports by purpose/date/demographics.

6) Users and permissions
- Username, password, role (owner/admin/editor/viewer).
- Auto-logout for owner/admin after timeout; mandatory authentication; soft delete and activation.

7) Config options
- Manage categories and values (education, occupation, community, religion, economic_status, supporter_status, transaction_purpose).
- Validate uniqueness; custom display ordering; updates cascade to references.

8) Export
- CSV and PDF; background processing with progress; templates; anonymization options.

9) Non-functional
- Startup: cold ≤ 3s, warm ≤ 1.5s.
- 100k record rendering under 100ms via virtualization.
- SQLite with WAL and indexing; sanitize inputs; secure logs with PII redaction.

10) Integrity and audit
- Soft delete with timestamps and user attribution.
- Conflict resolution; audit trails; backup/restore with referential integrity.

## Data and Schema
- SQLite with WAL.
- Tables: polling_stations, sections, voters, voters_fts (FTS5), voter_turnout, transactions, config_options, users.
- ULID primary keys.
- Soft delete via deleted_at, unique constraints use WHERE deleted_at IS NULL.
- FTS triggers keep index in sync.
- Constraints:
  - EPIC unique among active; ********** format.
  - Birth year range: 1900..(current-18).
  - Turnout: one per voter per year.
  - Section must belong to voter’s polling station.

## IPC and Validation
- Typed IPC channels (e.g., Voter.Search, Voter.GetById, Voter.Upsert, Import.Start, Export.Start).
- Zod validation on all IPC payloads and at all layers:
  1) Client-side form validation.
  2) Preload type checking before IPC send.
  3) Service-level business validation.
  4) Repository/DB constraints.

## State Management
- Zustand stores: uiStore, voterStore, searchStore, importStore, authStore, configStore.
- TanStack Query for server-state caching, retries, optimistic updates.
- TanStack Virtual + TanStack Table for performant UI.

## Background Workers
- CSV import worker (streaming, validation, checkpoints, error reporting).
- PDF export worker (chunked generation, pagination).
- FTS index maintenance worker if needed.
- Job progress tracking with resumable operations.

## Error Handling
- DomainError with codes: VALIDATION_ERROR, CONFLICT, NOT_FOUND, PERMISSION_DENIED, TRANSIENT_FAILURE.
- Repository → DB errors; Service → business errors; IPC → transport/serialization; UI → user-friendly messages.
- Logs redact PII.

## Security Rules
- No direct DB access from renderer; only via IPC.
- Enforce role-based authorization in services.
- Passcode/auth required for all users.
- Auto-logout for owner/admin.
- Keytar for secure credential storage where applicable.

## Performance Targets
- Search latency ≤ 300ms; render large tables ≤ 100ms.
- Indexed queries; prepared statements; connection pooling patterns.
- Debounced search inputs; virtualized rendering.

## Naming Conventions
- Code style: lowerCamelCase for variables/functions/properties; PascalCase for classes/types/React components/Zod schemas; UPPER_SNAKE_CASE for constants.
- Files/dirs: kebab-case filenames and directories. Tests mirror paths and end with .test.ts.
- Imports: always use "@/..." alias for internal imports across app and tests. Do not add new alias prefixes.
- DB vs DTO: SQLite columns snake_case; app-facing DTOs lowerCamelCase. Repositories map between them.
- Class naming: Repository classes end with Repository; service classes end with Service.
- IPC: Channel ids as Domain.Action (e.g., Voter.Search); IPC payload fields lowerCamelCase.

## Implementation Plan Guidance (mapping tasks)
- Follow tasks in docs/voter-management-system/tasks.md with TDD:
  - DB and schema with tests (ULID, FTS5, indexes).
  - Secure IPC and Zod schemas/tests.
  - Repository and service layers with tests.
  - Zustand stores and UI components with tests.
  - Import/export workers with tests and progress.
  - Auth and RBAC; Config management; Turnout; Transactions.
  - Error handling; performance and E2E test coverage.
  - Final integration and system testing.

## Guardrails
- Keep FTS triggers in sync with voters table.
- Maintain soft-delete-aware unique indexes.
- Validate and sanitize all inputs.
- Strict TypeScript; ESLint + Prettier.
- Do not disable contextIsolation or enable nodeIntegration in renderer.

## Import & Path Alias Guidelines
- Use the "@" alias for all internal imports (configured in both Vite and tsconfig).
- Replace all relative imports with "@/..." in both app and tests.
  - E.g. use '@/shared/types/errors' instead of '../../shared/types/errors'
- Applies to all TypeScript/JavaScript in src/ and electron/ (where modules resolve under src).
- Do not add new alias prefixes unless configured in both Vite and tsconfig.

## Package Manager
- pnpm