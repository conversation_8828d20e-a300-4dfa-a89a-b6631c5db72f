{"version": "6", "dialect": "sqlite", "id": "3e042651-67a5-4931-9866-f31164a859dc", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"config_options": {"name": "config_options", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "polling_stations": {"name": "polling_stations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sections": {"name": "sections", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "polling_station_id": {"name": "polling_station_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"sections_polling_station_id_polling_stations_id_fk": {"name": "sections_polling_station_id_polling_stations_id_fk", "tableFrom": "sections", "tableTo": "polling_stations", "columnsFrom": ["polling_station_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "transactions": {"name": "transactions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "voter_id": {"name": "voter_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "transaction_date": {"name": "transaction_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "purpose": {"name": "purpose", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"transactions_voter_id_voters_id_fk": {"name": "transactions_voter_id_voters_id_fk", "tableFrom": "transactions", "tableTo": "voters", "columnsFrom": ["voter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_login": {"name": "last_login", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_expires": {"name": "session_expires", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "voter_turnout": {"name": "voter_turnout", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "voter_id": {"name": "voter_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "election_year": {"name": "election_year", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "voted": {"name": "voted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"voter_turnout_voter_id_voters_id_fk": {"name": "voter_turnout_voter_id_voters_id_fk", "tableFrom": "voter_turnout", "tableTo": "voters", "columnsFrom": ["voter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "voters": {"name": "voters", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "relationship_type": {"name": "relationship_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "relationship_name": {"name": "relationship_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "birth_year": {"name": "birth_year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "epic_number": {"name": "epic_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "house_number": {"name": "house_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "polling_station_id": {"name": "polling_station_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "section_id": {"name": "section_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "facebook": {"name": "facebook", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "instagram": {"name": "instagram", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "twitter": {"name": "twitter", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "supporter_status": {"name": "supporter_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "education": {"name": "education", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "occupation": {"name": "occupation", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "community": {"name": "community", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "religion": {"name": "religion", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "economic_status": {"name": "economic_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "custom_notes": {"name": "custom_notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"voters_polling_station_id_polling_stations_id_fk": {"name": "voters_polling_station_id_polling_stations_id_fk", "tableFrom": "voters", "tableTo": "polling_stations", "columnsFrom": ["polling_station_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "voters_section_id_sections_id_fk": {"name": "voters_section_id_sections_id_fk", "tableFrom": "voters", "tableTo": "sections", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}