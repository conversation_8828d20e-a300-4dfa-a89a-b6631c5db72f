CREATE TABLE `config_options` (
	`id` text PRIMARY KEY NOT NULL,
	`category` text NOT NULL,
	`value` text NOT NULL,
	`display_name` text NOT NULL,
	`display_order` integer NOT NULL,
	`active` integer NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text
);
--> statement-breakpoint
CREATE TABLE `polling_stations` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`code` text NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text
);
--> statement-breakpoint
CREATE TABLE `sections` (
	`id` text PRIMARY KEY NOT NULL,
	`polling_station_id` text NOT NULL,
	`name` text NOT NULL,
	`code` text NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	FOREIGN KEY (`polling_station_id`) REFERENCES `polling_stations`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `transactions` (
	`id` text PRIMARY KEY NOT NULL,
	`voter_id` text NOT NULL,
	`transaction_date` text NOT NULL,
	`purpose` text NOT NULL,
	`amount` integer NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	FOREIGN KEY (`voter_id`) REFERENCES `voters`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` text PRIMARY KEY NOT NULL,
	`username` text NOT NULL,
	`password_hash` text NOT NULL,
	`role` text NOT NULL,
	`full_name` text,
	`active` integer NOT NULL,
	`last_login` text,
	`session_expires` text,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text
);
--> statement-breakpoint
CREATE TABLE `voter_turnout` (
	`id` text PRIMARY KEY NOT NULL,
	`voter_id` text NOT NULL,
	`election_year` integer NOT NULL,
	`voted` integer NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	FOREIGN KEY (`voter_id`) REFERENCES `voters`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `voters` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`relationship_type` text NOT NULL,
	`relationship_name` text NOT NULL,
	`gender` text NOT NULL,
	`birth_year` integer,
	`epic_number` text NOT NULL,
	`house_number` text NOT NULL,
	`polling_station_id` text NOT NULL,
	`section_id` text,
	`phone` text,
	`email` text,
	`facebook` text,
	`instagram` text,
	`twitter` text,
	`status` text NOT NULL,
	`supporter_status` text,
	`education` text,
	`occupation` text,
	`community` text,
	`religion` text,
	`economic_status` text,
	`custom_notes` text,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL,
	`deleted_at` text,
	FOREIGN KEY (`polling_station_id`) REFERENCES `polling_stations`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`section_id`) REFERENCES `sections`(`id`) ON UPDATE no action ON DELETE no action
);
