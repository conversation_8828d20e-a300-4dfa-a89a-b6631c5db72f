-- 0002_export_jobs.sql
-- Create export_jobs table for background export tasks
-- ULID text primary key, JSON-like fields stored as TEXT (stringified JSON)
-- Follows project rules: snake_case columns, soft-constraints, and helpful indexes

BEGIN TRANSACTION;

CREATE TABLE IF NOT EXISTS export_jobs (
  id TEXT PRIMARY KEY, -- ULID
  status TEXT NOT NULL CHECK (status IN ('pending','running','completed','failed','canceled')),
  progress INTEGER NOT NULL DEFAULT 0 CHECK (progress BETWEEN 0 AND 100),

  message TEXT NULL,
  result_path TEXT NULL,
  error_report_path TEXT NULL,

  -- Store JSON as TEXT; application layer serializes/deserializes
  options TEXT NULL,
  filters TEXT NULL,
  checkpoint TEXT NULL,

  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ','now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ','now')),
  completed_at TEXT NULL
);

-- Indexes to speed up dashboards / status pages
CREATE INDEX IF NOT EXISTS idx_export_jobs_status ON export_jobs (status);
CREATE INDEX IF NOT EXISTS idx_export_jobs_created_at ON export_jobs (created_at);

COMMIT;