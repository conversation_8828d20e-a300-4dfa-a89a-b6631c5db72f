-- 0001_fts_and_indexes.sql
-- Add soft-delete-aware unique/performance indexes and FTS5 virtual table with triggers

-- Ensure foreign keys on
PRAGMA foreign_keys = ON;

-- =====================
-- Soft-delete-aware unique + performance indexes
-- =====================

-- Users: username unique among active
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users(username) WHERE deleted_at IS NULL;

-- Voters: EPIC unique among active
CREATE UNIQUE INDEX IF NOT EXISTS idx_voters_epic ON voters(epic_number) WHERE deleted_at IS NULL;

-- Voters: performance filters
CREATE INDEX IF NOT EXISTS idx_voters_polling_station ON voters(polling_station_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_voters_section ON voters(section_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_voters_status ON voters(status) WHERE deleted_at IS NULL;

-- Turnout: one per voter per election year (soft-delete aware)
CREATE UNIQUE INDEX IF NOT EXISTS idx_turnout_voter_year ON voter_turnout(voter_id, election_year) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_turnout_year ON voter_turnout(election_year) WHERE deleted_at IS NULL;

-- Transactions: performance
CREATE INDEX IF NOT EXISTS idx_transactions_voter ON transactions(voter_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_transactions_purpose ON transactions(purpose) WHERE deleted_at IS NULL;

-- Config lookups
CREATE INDEX IF NOT EXISTS idx_config_category ON config_options(category) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_config_active ON config_options(active, display_order) WHERE deleted_at IS NULL;

-- =====================
-- FTS5 virtual table and triggers
-- =====================

-- Note: Using content=voters and content_rowid=id for external content table
CREATE VIRTUAL TABLE IF NOT EXISTS voters_fts USING fts5(
  name,
  relationship_name,
  epic_number,
  house_number,
  phone,
  custom_notes,
  content=voters,
  content_rowid=id
);

-- Triggers to keep FTS index in sync
CREATE TRIGGER IF NOT EXISTS voters_fts_ai AFTER INSERT ON voters BEGIN
  INSERT INTO voters_fts(rowid, name, relationship_name, epic_number, house_number, phone, custom_notes)
  VALUES (new.id, new.name, new.relationship_name, new.epic_number, new.house_number, new.phone, new.custom_notes);
END;

CREATE TRIGGER IF NOT EXISTS voters_fts_ad AFTER DELETE ON voters BEGIN
  DELETE FROM voters_fts WHERE rowid = old.id;
END;

CREATE TRIGGER IF NOT EXISTS voters_fts_au AFTER UPDATE ON voters BEGIN
  DELETE FROM voters_fts WHERE rowid = old.id;
  INSERT INTO voters_fts(rowid, name, relationship_name, epic_number, house_number, phone, custom_notes)
  VALUES (new.id, new.name, new.relationship_name, new.epic_number, new.house_number, new.phone, new.custom_notes);
END;
