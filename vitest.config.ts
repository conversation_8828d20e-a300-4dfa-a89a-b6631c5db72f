// vitest.config.ts
import { defineConfig } from 'vitest/config';
import path from 'node:path';

export default defineConfig({
 test: {
   include: [
     'test/**/*.test.ts',
     'test/**/*.int.test.ts'
   ],
   globals: true,
   environment: 'node',
   coverage: {
     provider: 'v8',
     reportsDirectory: './coverage',
     reporter: ['text', 'lcov']
   },
 },
 esbuild: {
   include: /\.(ts|tsx|js|jsx)$/,
   exclude: [],
 },
 resolve: {
   alias: {
     '@': path.resolve(__dirname, '.'),
   },
 },
});
