<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Strict Content Security Policy for Electron Renderer.
         Notes:
         - 'self' only; no inline/eval. Renderer scripts are bundled modules.
         - connect-src allows ws: for Vite dev (removed in production by build config if desired).
         - img-src includes data: for inlined images/icons.
         - media-src/object-src denied.
         - base-uri 'none' to prevent base tag abuse.
         - form-action 'none' as app is local-only.
         Dev override:
         - style-src includes 'unsafe-inline' to accommodate React style props during development.
         - frame-ancestors removed from meta to avoid Chromium warning (it's ignored in meta).
         IMPORTANT: Revert these relaxations for production and enforce CSP via headers.
    -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self' ws:; base-uri 'none'; form-action 'none'; object-src 'none'; media-src 'none'">
    <title>Electixir</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
