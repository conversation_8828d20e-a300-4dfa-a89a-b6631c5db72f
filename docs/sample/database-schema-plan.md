# Database Schema Plan - Voter Management System

## Schema Overview

This document outlines the database schema design for the voter management system, optimized for offline-first operation with SQLite and designed to handle 100k+ voter records efficiently.

## Entity Relationship Diagram

```mermaid
erDiagram
    POLLING_STATIONS {
        string id PK "ULID"
        string name "Station name"
        string code "Unique station code"
        datetime created_at "Creation timestamp"
        datetime updated_at "Last update timestamp"
        datetime deleted_at "Soft delete timestamp"
    }

    SECTIONS {
        string id PK "ULID"
        string polling_station_id FK "Reference to polling station"
        string name "Section name/number"
        string code "Section code (1,2,3,etc for sorting)"
        datetime created_at "Creation timestamp"
        datetime updated_at "Last update timestamp"
        datetime deleted_at "Soft delete timestamp"
    }

    VOTERS {
        string id PK "ULID"
        string name "Full name"
        string relationship_type "Father/Mother/Husband/Others"
        string relationship_name "Name of relation"
        string gender "Male/Female/Other"
        int birth_year "Birth year (1900 to current-18)"
        string epic_number "Unique EPIC (**********)"
        string address "House number/address"
        string polling_station_id FK "Reference to polling station"
        string section_id FK "Reference to section (nullable)"
        string phone "Phone number"
        string email "Email address"
        string facebook "Facebook handle"
        string instagram "Instagram handle"
        string twitter "Twitter handle"
        string status "Active/Expired/Shifted/Missing/Duplicate/Disqualified"
        string supporter_status "From config_options"
        string education "From config_options"
        string occupation "From config_options"
        string community "From config_options"
        string religion "From config_options"
        string economic_status "From config_options"
        text custom_notes "Free text notes"
        datetime created_at "Creation timestamp"
        datetime updated_at "Last update timestamp"
        datetime deleted_at "Soft delete timestamp"
    }

    VOTERS_FTS {
        string rowid "FTS5 rowid"
        string name "Searchable name"
        string epic_number "Searchable EPIC"
        string address "Searchable address"
        string phone "Searchable phone"
        string custom_notes "Searchable notes"
    }

    VOTER_TURNOUT {
        string id PK "ULID"
        string voter_id FK "Reference to voter"
        int election_year "Election year"
        boolean voted "Whether voter participated"
        datetime created_at "Creation timestamp"
        datetime updated_at "Last update timestamp"
        datetime deleted_at "Soft delete timestamp"
    }

    TRANSACTIONS {
        string id PK "ULID"
        string voter_id FK "Reference to voter"
        date transaction_date "Date of transaction"
        string purpose "From config_options"
        int amount "Amount in integer (no decimals)"
        datetime created_at "Creation timestamp"
        datetime updated_at "Last update timestamp"
        datetime deleted_at "Soft delete timestamp"
    }

    CONFIG_OPTIONS {
        string id PK "ULID"
        string category "supporter_status/education/occupation/etc"
        string value "Option value"
        string display_name "Human readable name"
        int display_order "Sort order"
        boolean active "Active status"
        datetime created_at "Creation timestamp"
        datetime updated_at "Last update timestamp"
        datetime deleted_at "Soft delete timestamp"
    }

    USERS {
        string id PK "ULID"
        string username "Unique username"
        string password_hash "Hashed password"
        string role "owner/admin/editor/viewer"
        string full_name "User's full name"
        boolean active "Account active status"
        datetime last_login "Last login timestamp"
        datetime session_expires "Session expiry"
        datetime created_at "Creation timestamp"
        datetime updated_at "Last update timestamp"
        datetime deleted_at "Soft delete timestamp"
    }

    %% Relationships
    POLLING_STATIONS ||--o{ SECTIONS : "has sections"
    POLLING_STATIONS ||--o{ VOTERS : "contains voters"
    SECTIONS ||--o{ VOTERS : "organizes voters"
    VOTERS ||--o{ VOTER_TURNOUT : "has turnout records"
    VOTERS ||--o{ TRANSACTIONS : "has transactions"
    VOTERS ||--|| VOTERS_FTS : "indexed by"
    CONFIG_OPTIONS ||--o{ VOTERS : "categorizes"
    CONFIG_OPTIONS ||--o{ TRANSACTIONS : "defines purposes"
```

## Table Specifications

### Core Tables

#### 1. POLLING_STATIONS
**Purpose**: Master data for polling stations
**Key Features**:
- ULID primary keys for offline-first design
- Soft delete support
- Hierarchical organization (district → constituency → station)

#### 2. SECTIONS
**Purpose**: Sections within polling stations
**Key Features**:
- Optional relationship (voters can be "Unassigned")
- Display ordering for UI presentation
- Linked to specific polling stations

#### 3. VOTERS (Main Entity)
**Purpose**: Core voter information and political data
**Key Features**:
- Comprehensive personal information
- Political preferences via configurable categories
- Social media integration
- EPIC number uniqueness with format validation
- Status-based soft deletion (Duplicate/Disqualified auto-deleted)

#### 4. VOTERS_FTS (Virtual Table)
**Purpose**: Full-text search optimization
**Key Features**:
- FTS5 virtual table for fast text search
- Automatic triggers maintain consistency
- Searches across names, EPIC numbers, addresses, phone, notes

### Operational Tables

#### 5. VOTER_TURNOUT
**Purpose**: Election participation tracking
**Key Features**:
- One record per voter per election year constraint
- Simple voted/not voted tracking

#### 6. TRANSACTIONS
**Purpose**: Financial transaction records
**Key Features**:
- Integer amounts (no decimal handling)
- Configurable transaction purposes
- Simple chronological tracking

### System Tables

#### 7. CONFIG_OPTIONS
**Purpose**: System configuration and categorization
**Key Features**:
- Category-based organization
- Display ordering and activation
- Referenced by voter fields (supporter_status, education, etc.)

#### 8. USERS
**Purpose**: Local authentication and role management
**Key Features**:
- Role-based access control
- Session management
- Password security (hashed storage)

## Indexing Strategy

### Primary Indexes
```sql
-- All tables have ULID primary keys
CREATE UNIQUE INDEX idx_polling_stations_pk ON polling_stations(id);
CREATE UNIQUE INDEX idx_sections_pk ON sections(id);
-- ... etc for all tables
```

### Performance Indexes
```sql
-- Voter search and filtering
CREATE INDEX idx_voters_polling_station ON voters(polling_station_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_voters_section ON voters(section_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_voters_epic ON voters(epic_number) WHERE deleted_at IS NULL;
CREATE INDEX idx_voters_status ON voters(status) WHERE deleted_at IS NULL;

-- Turnout queries
CREATE UNIQUE INDEX idx_turnout_voter_year ON voter_turnout(voter_id, election_year) WHERE deleted_at IS NULL;
CREATE INDEX idx_turnout_year ON voter_turnout(election_year) WHERE deleted_at IS NULL;

-- Transaction queries
CREATE INDEX idx_transactions_voter ON transactions(voter_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_date ON transactions(transaction_date) WHERE deleted_at IS NULL;
CREATE INDEX idx_transactions_purpose ON transactions(purpose) WHERE deleted_at IS NULL;

-- Config lookups
CREATE INDEX idx_config_category ON config_options(category) WHERE deleted_at IS NULL;
CREATE INDEX idx_config_active ON config_options(active, display_order) WHERE deleted_at IS NULL;

-- User authentication
CREATE UNIQUE INDEX idx_users_username ON users(username) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_role ON users(role) WHERE deleted_at IS NULL;
```

### Full-Text Search Setup
```sql
-- FTS5 virtual table
CREATE VIRTUAL TABLE voters_fts USING fts5(
    name,
    relationship_name,
    epic_number,
    house_number,
    phone,
    custom_notes,
    content='voters',
    content_rowid='id'
);

-- Triggers to maintain FTS consistency
CREATE TRIGGER voters_fts_insert AFTER INSERT ON voters BEGIN
    INSERT INTO voters_fts(rowid, name, epic_number, address, phone, custom_notes)
    VALUES (new.id, new.name, new.epic_number, new.address, new.phone, new.custom_notes);
END;

CREATE TRIGGER voters_fts_delete AFTER DELETE ON voters BEGIN
    DELETE FROM voters_fts WHERE rowid = old.id;
END;

CREATE TRIGGER voters_fts_update AFTER UPDATE ON voters BEGIN
    DELETE FROM voters_fts WHERE rowid = old.id;
    INSERT INTO voters_fts(rowid, name, epic_number, address, phone, custom_notes)
    VALUES (new.id, new.name, new.epic_number, new.address, new.phone, new.custom_notes);
END;
```

## Constraints and Validation

### Business Rules
1. **EPIC Number**: Unique among active voters, format **********
2. **Birth Year**: Between 1900 and (current_year - 18)
3. **Turnout**: One record per voter per election year
4. **Status Handling**: Duplicate/Disqualified voters auto-soft-deleted
5. **Relationships**: Section must belong to the same polling station as voter

### Data Integrity
- Foreign key constraints with CASCADE on update
- Check constraints for enum values
- Unique constraints with soft-delete awareness
- NOT NULL constraints on required fields

## Performance Considerations

### SQLite Optimizations
- WAL (Write-Ahead Logging) mode for better concurrency
- PRAGMA settings for performance tuning
- Connection pooling for multiple operations
- Prepared statements for repeated queries

### Query Optimization
- Strategic indexing for common access patterns
- FTS5 for text search instead of LIKE queries
- Composite indexes for multi-column filters
- Covering indexes where beneficial

### Scalability Features
- ULID primary keys for distributed/offline scenarios
- Soft delete for data recovery and audit trails
- Timestamp tracking for synchronization
- Configurable categories for flexibility

This schema design balances performance, flexibility, and data integrity while supporting the offline-first architecture and 100k+ record scalability requirements.