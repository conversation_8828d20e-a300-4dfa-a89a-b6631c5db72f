# Documenting Implementation

- Username unique as-is (case-sensitive) and trimmed; password minimum 8 chars, at least 1 letter and 1 number
- Role defaults to “viewer” for offline creation; soft-deleted usernames stay reserved, preventing ID collisions when you sync with Supabase later.

Proceeding to write tests. Confirm final inputs/outputs for AuthService methods:

- createUser(input: { username: string; password: string; role?: "owner"|"admin"|"editor"|"viewer" }) -> Promise<{ id: string; username: string; role: "owner"|"admin"|"editor"|"viewer"; active: boolean }>
- authenticate(username: string, password: string) -> Promise<{ id: string; username: string; role: "owner"|"admin"|"editor"|"viewer"; active: boolean }> or DomainError with INVALID_CREDENTIALS
- activate(userId: string) -> Promise<{ id; username; role; active }>
- deactivate(userId: string) -> Promise<{ id; username; role; active }>
- softDelete(userId: string) -> Promise<{ id; username; role; active }> (active stays false)