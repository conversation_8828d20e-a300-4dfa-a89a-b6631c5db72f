# Export Subsystem Documentation

This document explains the Export subsystem for the Voter Management System, covering IPC contracts, schemas, repository, worker, filters, anonymization, and operational notes. It is intended for developers and QA engineers to understand, extend, and test the export pipeline.

## Goals

- Offline-first CSV export of voters with optional anonymization.
- Large dataset handling via paging (streaming by batches).
- Observable progress and resumability via DB-backed job records.
- Cancelable background execution with clean terminal states.

## Architecture Overview

Layers:
1) IPC (electron/main.ts)
   - Plain channels for Export.* (errors bubble, no envelope).
   - Validates requests using Zod (src/shared/types/ipc-export.ts).
   - Creates jobs via ExportJobRepository and triggers the background worker.
   - Exposes global repo accessors to avoid circular dependencies in worker.

2) Repository (src/main/repositories/export-job-repository.ts)
   - Table: export_jobs (migration 0002_export_jobs.sql).
   - Methods: create, getById, update, cancel.
   - Fields: id, status, progress, message, result_path, error_report_path, options, filters, checkpoint, created_at, updated_at, completed_at.

3) Worker (src/main/workers/export/runner.ts)
   - startExportJob(params): CSV generation loop (batched), anonymization, progress, cancellation, checkpointing.
   - Uses safeUpdate via global __getExportJobRepo / __updateExportJob to avoid circular deps.

4) Shared Types & Schemas
   - src/shared/types/ipc-export.ts: Zod schemas for Start/Status/Cancel including filter validation.
   - src/shared/types/ipc-channels.ts: Central registry maps Export.* to the above Zod schemas.

## IPC Contracts

Channels are plain (non-enveloped). Validation is via Zod in IPCRequestSchemas.

- Export.Start
  - Request: { type: 'csv' | 'pdf'; anonymize?: boolean; filters?: { pollingStationId?, sectionId?, status? } }
  - Response: { jobId: string }
  - Behavior:
    - Creates a job row with status 'pending'.
    - For type 'csv': launches background worker startExportJob({jobId, anonymize, filters}).
    - For type 'pdf': currently not implemented; job is marked failed with a clear message.

- Export.Status
  - Request: { id: string }
  - Response: {
      id, status, progress, message, resultPath, errorReportPath,
      options?, filters?, checkpoint?, createdAt, updatedAt, completedAt?
    }
  - Behavior: Reads job row and maps snake_case (DB) to camelCase DTO; throws NOT_FOUND when missing.

- Export.Cancel
  - Request: { id: string }
  - Response: null
  - Behavior: Sets status to 'canceled', updates timestamps. Idempotent; throws NOT_FOUND for unknown job id.

## Zod Schemas

Defined in src/shared/types/ipc-export.ts.

- ExportStartRequestSchema
  - type: enum('csv', 'pdf')
  - anonymize: boolean (default false)
  - filters: object (optional, nullable)
    - pollingStationId?: string
    - sectionId?: string
    - status?: enum('Active', 'Expired', 'Shifted', 'Missing', 'Duplicate', 'Disqualified')

- ExportStatusRequestSchema
  - id: string (min(1))

- ExportCancelRequestSchema
  - id: string (min(1))

- ExportStatusResponseSchema
  - Mirrors job status with camelCase fields and typed filters shape.

Registered in src/shared/types/ipc-channels.ts:
- 'Export.Start' -> ExportStartRequestSchema
- 'Export.Status' -> ExportStatusRequestSchema
- 'Export.Cancel' -> ExportCancelRequestSchema

## Repository

File: src/main/repositories/export-job-repository.ts

Responsibilities:
- create({ type, anonymize, filters }): returns { id }
- getById(id): returns DB row (snake_case) or null
- update(id, patch): merges fields (clamps progress, sets timestamps)
- cancel(id): sets status 'canceled' and timestamps

Notes:
- Uses a minimal SQL-escaping helper for string interpolation consistent with current db client.
- ULID generation is currently provided via a local generator function; call sites use ULID().

DB Schema (drizzle/migrations/0002_export_jobs.sql):
- Columns:
  id TEXT PRIMARY KEY, status TEXT, progress REAL, message TEXT,
  result_path TEXT, error_report_path TEXT,
  options TEXT, filters TEXT, checkpoint TEXT,
  created_at TEXT, updated_at TEXT, completed_at TEXT
- Indexes on status, created_at.

## Worker

File: src/main/workers/export/runner.ts
Entry: startExportJob({ jobId, anonymize?, filters?, outputDir? })

Pipeline:
1) Marks job 'running', message 'Starting export'.
2) Resolves output dir (default ./tmp/exports) and creates CSV file.
3) Writes header row: id,name,relation_type,relation_name,house_number,birth_year,gender,epic_number,polling_station,section,status,created_at,updated_at.
4) Loops in pages:
   - Fetches a batch via fetchVotersBatch({ afterId, limit, filters }).
   - Applies anonymizeRow when requested:
     - Masks name to first/last with middle asterisks.
     - Masks epic_number leaving first 3 chars.
     - Replaces digits in house_number with asterisks.
   - Appends CSV lines.
   - Updates job progress and checkpoint with { lastId, rows, bytes }.
   - Checks for cancelation by inspecting job status via repo.getById.
5) On completion:
   - Sets status 'completed', progress 100, message, result_path, completed_at.
6) On cancellation:
   - Sets status 'canceled', message 'Export canceled', completed_at.
7) On error:
   - Sets status 'failed', message with error details, completed_at.

Filtering:
- Filters applied in both countVoters() and fetchVotersBatch():
  - pollingStationId -> WHERE polling_station_id = ?
  - sectionId -> WHERE section_id = ?
  - status -> WHERE status = ?
  - deleted_at IS NULL enforced.
- Pagination by id: WHERE id > lastId ORDER BY id LIMIT N.

Progress:
- If totalRows > 0, progress = (rowsWritten / totalRows) * 100.
- Else, a bounded heuristic to avoid stuck zero.

Globals:
- __getExportJobRepo(): Export repo instance provided by IPC module.
- __updateExportJob(): Fallback to update job in environments lacking repo.update.

## Security & Privacy

- Renderer process cannot access filesystem directly; only via IPC.
- Export filters validated by Zod; worker SQL is constructed with basic escaping consistent with current db client (migrate to parameterized queries when supported).
- Anonymization optional; masks PII including name, epic_number, digits in house_number.
- Consider adding CSP and secure temp directories for output in production builds.

## Performance Considerations

- Batch size default: 1000 records (tunable).
- ORDER BY id paging ensures stable forward-only traversal.
- File writes are append-based; ensure disk I/O quotas where applicable.
- SQLite: Ensure WAL mode enabled globally for concurrent read/write performance.

## Testing

File: test/ipc/export-ipc.test.ts
- Export.Start returns jobId; immediate Export.Status shows job existence and DTO fields.
- Export.Start validates type and rejects invalid values.
- Export.Status throws NOT_FOUND for unknown id.
- Export.Cancel cancels existing job; status accepts races (completed/failed/pending/running/canceled).
- Future: Add integration tests verifying actual CSV content, filters effect, anonymization, and progress reaching 100.

## Extensibility Roadmap

- PDF export worker implementation with pagination and layout templates.
- Parameterized SQL via db client upgrade; introduce query builder or prepared statements.
- Rich filter set: date ranges, demographics (config_options), and FTS-based text search.
- Background job orchestration with retries and better error report artifacts.
- Progress event streaming (optional) to renderer for live updates.

## Operational Notes

- Output directory default: ./tmp/exports (configurable by outputDir parameter in startExportJob).
- Job status mapping in IPC converts DB snake_case to DTO camelCase.
- Cancelation is cooperative: takes effect at batch boundaries.
- Ensure migrations are applied (pnpm db:migrate) so export_jobs table exists before use.