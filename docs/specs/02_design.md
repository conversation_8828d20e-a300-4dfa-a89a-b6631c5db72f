# Design Document

## Overview

The Voter Management System is an offline-first Electron application built with React, TypeScript, and SQLite. The system follows a secure, layered architecture with strict separation between the main process (Node.js) and renderer process (React UI). The design emphasizes performance, security, and maintainability while supporting up to 100,000+ voter records.

### Key Architectural Principles

- **Offline-First**: All core functionality works without internet connectivity
- **Security-First**: Context isolation, typed IPC, and data validation at every boundary
- **Performance-Optimized**: TanStack Virtual for rendering, indexed database queries, and background processing
- **Type-Safe**: End-to-end TypeScript with runtime validation using Zod schemas
- **Modular**: Clean separation of concerns with repository pattern and service layers
- **Modern Stack**: TanStack ecosystem (Virtual, Query, Table) for optimal performance and developer experience

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Renderer Process (React)"
        UI[React UI Components]
        Store[Zustand State Management]
        Workers[Web Workers]
        API[Typed IPC Client]
    end

    subgraph "Main Process (Node.js)"
        IPC[IPC Handlers]
        Services[Business Services]
        Repos[Repository Layer]
        DB[(SQLite Database)]
    end

    subgraph "Security Boundary"
        Preload[Preload Script]
        Bridge[Context Bridge]
    end

    UI --> Store
    Store --> API
    API --> Bridge
    Bridge --> Preload
    Preload --> IPC
    IPC --> Services
    Services --> Repos
    Repos --> DB

    Workers --> UI
```

### Process Architecture

1. **Main Process**: Handles database operations, file system access, and business logic
2. **Renderer Process**: Manages UI, user interactions, and client-side state
3. **Preload Script**: Provides secure, typed bridge between main and renderer processes
4. **Web Workers**: Handle CPU-intensive tasks like CSV parsing and PDF generation

## Components and Interfaces

### Database Layer (SQLite with WAL Mode)

**Core Tables:**
- `polling_stations`: Polling station information
- `sections`: Sections within polling stations (nullable for "Unassigned")
- `voters`: Main voter records with personal and political information
- `voter_turnout`: Election turnout tracking
- `transactions`: Financial transaction records
- `config_options`: Configurable system categories and values
- `users`: Local user authentication and role management

**Full-Text Search:**
- `voters_fts`: FTS5 virtual table for fast text search across voter names, relationships, and addresses
- Automatic triggers to maintain FTS index consistency

**Performance Features:**
- WAL (Write-Ahead Logging) mode for better concurrency
- Strategic indexes on frequently queried columns
- ULID primary keys for offline-first design

### Repository Layer

**Base Repository Pattern:**
```typescript
interface BaseRepository<T> {
  findById(id: string): Promise<T | null>;
  findMany(filters: FilterOptions): Promise<T[]>;
  create(data: CreateData<T>): Promise<T>;
  update(id: string, data: UpdateData<T>): Promise<T>;
  softDelete(id: string): Promise<void>;
}
```

**Specialized Repositories:**
- `VoterRepository`: Handles voter CRUD with FTS integration
- `PollingStationRepository`: Manages polling stations and sections
- `TurnoutRepository`: Tracks election participation
- `TransactionRepository`: Financial record management
- `ConfigRepository`: System configuration management
- `UserRepository`: Authentication and user management

### Service Layer

**Business Logic Services:**
- `VoterService`: Voter management with validation and business rules
- `ImportService`: CSV import processing with validation and error handling
- `ExportService`: Data export in multiple formats (CSV, PDF)
- `SearchService`: Full-text search with filtering and pagination
- `AuthService`: User authentication and session management
- `ConfigService`: System configuration management

### IPC Communication Layer

**Secure IPC Design:**
- Context isolation enabled with typed preload script
- Zod schema validation for all IPC payloads
- Request/response pattern with correlation IDs
- Error handling with domain-specific error types

**IPC Channel Structure:**
```typescript
// Channel naming: Domain.Action
interface IPCChannels {
  'Voter.Search': { request: SearchRequest; response: SearchResponse };
  'Voter.GetById': { request: { id: string }; response: Voter | null };
  'Voter.Upsert': { request: VoterInput; response: Voter };
  'Import.Start': { request: ImportRequest; response: { jobId: string } };
  'Export.Start': { request: ExportRequest; response: { jobId: string } };
  // ... additional channels
}
```

### State Management (Zustand)

**Store Slices (Used Everywhere):**
- `uiStore`: UI state, loading indicators, modal states
- `voterStore`: Voter data cache and optimistic updates
- `searchStore`: Search results and filter state
- `importStore`: Import job status and progress
- `authStore`: User session and permissions
- `configStore`: System configuration and category management

### Background Processing

**Web Workers:**
- `csvImport.worker.ts`: Streaming CSV parsing and validation
- `pdfExport.worker.ts`: PDF generation with pagination
- `ftsIndex.worker.ts`: Full-text search index maintenance

**Job Management:**
- Checkpointing for resumable operations
- Progress tracking with real-time updates
- Error collection and reporting

## Data Models

### Core Entity Models

**Voter Model:**
```typescript
interface Voter {
  id: string; // ULID
  name: string;
  relationshipType: 'Father' | 'Mother' | 'Husband' | 'Others';
  relationshipName: string;
  gender: 'Male' | 'Female' | 'Other';
  birthYear?: number;
  epicNumber: string; // Unique, format: **********
  houseNumber: string;
  pollingStationId: string;
  sectionId?: string; // Nullable for "Unassigned"
  phone?: string;
  email?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;
  status: 'Active' | 'Expired' | 'Shifted' | 'Missing' | 'Duplicate' | 'Disqualified';
  supporterStatus: string; // From config_options
  education?: string; // From config_options
  occupation?: string; // From config_options
  community?: string; // From config_options
  religion?: string; // From config_options
  economicStatus?: string; // From config_options
  customNotes?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}
```

**Validation Rules:**
- EPIC number: Strict format validation (3 uppercase letters + 7 digits)
- Birth year: Must be between 1900 and (current year - 18)
- Status handling: Duplicate/Disqualified records are automatically soft-deleted
- Required fields: name, polling_station_id
- Configurable fields validated against config_options table

### Database Schema Design

**Indexing Strategy:**
- Primary indexes on all ID fields (ULID)
- Composite indexes for common query patterns
- FTS5 indexes for text search
- Unique constraints with soft-delete awareness

**Soft Delete Implementation:**
- All tables include `deleted_at` timestamp
- Unique constraints use `WHERE deleted_at IS NULL`
- Views and queries default to excluding soft-deleted records

## Error Handling

### Error Classification

**Domain Errors:**
```typescript
enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CONFLICT = 'CONFLICT',
  NOT_FOUND = 'NOT_FOUND',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  TRANSIENT_FAILURE = 'TRANSIENT_FAILURE'
}

class DomainError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: any
  ) {
    super(message);
  }
}
```

**Error Handling Strategy:**
- Repository layer: Database-specific errors
- Service layer: Business logic validation
- IPC layer: Transport and serialization errors
- UI layer: User-friendly error messages

### Validation Pipeline

1. **Client-side validation**: Immediate feedback for form inputs
2. **Preload validation**: Type checking before IPC transmission
3. **Service validation**: Business rule enforcement
4. **Repository validation**: Database constraint verification

## Testing Strategy

### Test Coverage Goals
- Unit tests: 80%+ line coverage
- Critical paths: 100% coverage (repositories, services)
- Integration tests: IPC contracts and database operations
- E2E tests: Major user workflows

### Test Types

**Unit Tests:**
- Repository CRUD operations
- Service business logic
- Validation schemas
- Utility functions

**Integration Tests:**
- IPC end-to-end communication
- Database migrations
- Background job processing
- File import/export workflows

**Performance Tests:**
- Large dataset rendering (100k+ records)
- Search query performance
- Import/export throughput
- Memory usage under load

**Security Tests:**
- IPC payload validation
- SQL injection prevention
- Input sanitization
- Authentication flows

## Performance Optimizations

### Database Performance
- SQLite WAL mode for better concurrency
- Strategic indexing for common queries
- FTS5 for fast text search
- Connection pooling and prepared statements

### UI Performance
- TanStack Virtual for large table virtualization (modern, actively maintained)
- TanStack Table for advanced table features and data management
- TanStack Query for server state management and caching
- Optimistic updates with conflict resolution
- Debounced search inputs

### Memory Management
- Streaming CSV processing
- Chunked PDF generation
- Background garbage collection
- Resource cleanup in workers

### Caching Strategy
- TanStack Query for server state caching and synchronization
- Zustand for client state management (used everywhere)
- SQLite query result caching
- Static asset caching

## Security Considerations

### Electron Security
- Context isolation enabled
- Node.js integration disabled in renderer
- Strict Content Security Policy
- Secure preload script implementation

### Data Protection
- Local SQLite encryption (if device supports)
- PII redaction in logs
- Secure credential storage (keytar)
- Audit trail for sensitive operations

### Access Control
- Role-based permissions (owner, admin, editor, viewer)
- Session management with auto-logout
- Passcode requirements for all users
- Operation-level authorization checks

## Migration and Extensibility

### Database Migrations
- Drizzle ORM migration system
- Version-controlled schema changes
- Rollback capabilities
- Data migration scripts

### Future Supabase Integration
- Repository pattern enables easy cloud migration
- Event sourcing preparation for sync
- Conflict resolution strategies
- Offline-first design maintained

### Configuration Management
- Feature flags for gradual rollouts
- Environment-specific configurations
- Runtime configuration updates
- Schema validation for all configs

## Development Workflow

### Project Structure
```
src/
├── main/                 # Main process code
│   ├── database/        # Database setup and migrations
│   ├── repositories/    # Data access layer
│   ├── services/        # Business logic
│   ├── ipc/            # IPC handlers
│   └── security/       # Security utilities
├── renderer/           # Renderer process code
│   ├── components/     # React components
│   ├── stores/         # Zustand stores
│   ├── workers/        # Web workers
│   ├── lib/           # Utilities and helpers
│   └── types/         # TypeScript definitions
├── preload/           # Preload scripts
├── shared/            # Shared types and utilities
└── types/             # Global type definitions
```

### Build and Development
- Vite for fast development builds
- TypeScript strict mode
- ESLint and Prettier for code quality
- Hot reload for renderer process
- Automated testing in CI/CD

This design provides a robust, secure, and scalable foundation for the voter management system while maintaining the flexibility to evolve and integrate with cloud services in the future.