/**
 * In-memory Import Job Store
 * - Offline-first friendly; can be swapped with persisted store later.
 * - Exposes create, getStatus, update, and cancel.
 * - Validates inputs at IPC boundary; here we keep types minimal and pure TS.
 */

export type ImportJobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'canceled'

export interface ImportJobCounters {
  processed: number
  accepted: number
  rejected: number
}

export interface ImportJob {
  id: string
  status: ImportJobStatus
  progress: number
  counters: ImportJobCounters
  message: string | null
  errorReportPath: string | null
  createdAt: string
  updatedAt: string
  completedAt?: string | null
  // optional inputs for reference
  filePath?: string
  mapping?: Record<string, unknown>
  options?: Record<string, unknown>
}

function nowISO() {
  return new Date().toISOString()
}

// Simple ULID-ish fallback; in production prefer ulid() or crypto.randomUUID()
function genId(): string {
  if (typeof (globalThis as any).crypto?.randomUUID === 'function') {
    return (globalThis as any).crypto.randomUUID()
  }
  return 'imp_' + Math.random().toString(36).slice(2) + Date.now().toString(36)
}

class ImportJobStore {
  private jobs = new Map<string, ImportJob>()

  createJob(filePath: string, mapping: Record<string, unknown>, options: Record<string, unknown> = {}) {
    const id = genId()
    const ts = nowISO()
    const job: ImportJob = {
      id,
      status: 'pending',
      progress: 0,
      counters: { processed: 0, accepted: 0, rejected: 0 },
      message: null,
      errorReportPath: null,
      createdAt: ts,
      updatedAt: ts,
      completedAt: null,
      filePath,
      mapping,
      options,
    }
    this.jobs.set(id, job)
    return { id }
  }

  getStatus(id: string): ImportJob | null {
    return this.jobs.get(id) ?? null
  }

  update(id: string, patch: Partial<Omit<ImportJob, 'id' | 'createdAt'>>): ImportJob | null {
    const current = this.jobs.get(id)
    if (!current) return null
    const next: ImportJob = {
      ...current,
      ...patch,
      updatedAt: nowISO(),
    }
    // If status transitions to a terminal state, ensure completedAt
    if (['completed', 'failed', 'canceled'].includes(next.status) && !next.completedAt) {
      next.completedAt = next.updatedAt
    }
    // Clamp progress
    if (typeof next.progress === 'number') {
      next.progress = Math.max(0, Math.min(100, Math.floor(next.progress)))
    }
    this.jobs.set(id, next)
    return next
  }

  cancel(id: string): boolean {
    const current = this.jobs.get(id)
    if (!current) return false
    if (current.status === 'completed' || current.status === 'failed' || current.status === 'canceled') {
      return true
    }
    current.status = 'canceled'
    current.updatedAt = nowISO()
    current.completedAt = current.updatedAt
    current.message = current.message ?? 'Canceled by user'
    this.jobs.set(id, current)
    return true
  }
}

// Singleton store instance
export const importJobStore = new ImportJobStore()

// Convenience API wrappers
export function createJob(filePath: string, mapping: Record<string, unknown>, options: Record<string, unknown> = {}) {
  return importJobStore.createJob(filePath, mapping, options)
}
export function getStatus(id: string) {
  return importJobStore.getStatus(id)
}
export function updateJob(id: string, patch: Partial<Omit<ImportJob, 'id' | 'createdAt'>>) {
  return importJobStore.update(id, patch)
}
export function cancelJob(id: string) {
  return importJobStore.cancel(id)
}