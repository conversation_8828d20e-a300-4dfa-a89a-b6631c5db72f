/**
 * Repository-backed Import runner
 * - Advances progress and updates counters periodically in the import_jobs table.
 * - Persists checkpoints and supports idempotent resume.
 * - Designed to be replaced by a streaming CSV processor with backpressure.
 */

type RepoJobRow = {
  id: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'canceled'
  progress: number
  counters: any
  message: string | null
  error_report_path?: string | null
  created_at: string
  updated_at: string
  completed_at?: string | null
  checkpoint?: any | null
}

type ImportJobRepo = {
  getById(id: string): Promise<RepoJobRow | null>
  update(
    id: string,
    patch: Partial<{
      status: RepoJobRow['status']
      progress: number
      counters: any
      message: string | null
      error_report_path: string | null
      checkpoint: any | null
      completed_at: string | null
    }>
  ): Promise<void>
}

const TICK_MS = 50
const TOTAL_STEPS = 50 // 50 * 2% = 100%

const running = new Set<string>()

// The repository is resolved lazily from electron/main via a global accessor injected there
function getRepo(): ImportJobRepo {
  const g: any = globalThis as any
  // electron/main attaches this when wiring IPC to avoid circular deps
  if (typeof g.__getImportJobRepo !== 'function') {
    // Fallback to an in-memory shim if not present (test safety)
    return {
      async getById(_id: string) {
        return null
      },
      async update() {
        // no-op
      },
    }
  }
  const repo = g.__getImportJobRepo() as any
  // If repo.update is missing (older shim), provide a fallback via global __updateImportJob if present
  if (typeof repo.update !== 'function' && typeof g.__updateImportJob === 'function') {
    repo.update = async (id: string, patch: any) => {
      await g.__updateImportJob(id, patch)
    }
  }
  return repo as ImportJobRepo
}

async function safeGet(id: string): Promise<RepoJobRow | null> {
  try {
    return await getRepo().getById(id)
  } catch {
    return null
  }
}

async function setRunningIfPending(id: string) {
  const job = await safeGet(id)
  if (job && job.status === 'pending') {
    try {
      await getRepo().update(id, { status: 'running', message: 'Starting import' })
    } catch {
      // ignore if fallback not available; next ticks will attempt again
    }
  }
}

export function startImportJob(id: string) {
  if (running.has(id)) return
  running.add(id)

  // fire-and-forget
  void (async () => {
    await setRunningIfPending(id)

    // Resume from checkpoint if present
    let steps = 0
    const job0 = await safeGet(id)
    if (job0?.checkpoint && typeof job0.checkpoint.steps === 'number') {
      steps = Math.max(0, Math.min(TOTAL_STEPS, Number(job0.checkpoint.steps)))
    } else if (typeof job0?.progress === 'number') {
      steps = Math.round((job0.progress / 100) * TOTAL_STEPS)
    }

    const interval = setInterval(async () => {
      const job = await safeGet(id)
      if (!job) {
        clearInterval(interval)
        running.delete(id)
        return
      }

      // Stop if terminal state
      if (job.status === 'completed' || job.status === 'failed' || job.status === 'canceled') {
        clearInterval(interval)
        running.delete(id)
        return
      }

      steps += 1
      const prevCounters = job.counters ?? {}
      // Normalize to expected shape for Status DTO mapping in electron/main.ts
      const processed = (prevCounters.processed ?? 0) + 10
      const accepted = (prevCounters.accepted ?? 0) + 9
      const rejected = (prevCounters.rejected ?? 0) + 1
      const progress = Math.min(100, Math.floor((steps / TOTAL_STEPS) * 100))

      const checkpoint = { steps }

      try {
        await getRepo().update(id, {
          status: 'running',
          progress,
          counters: { processed, accepted, rejected },
          message: `Processing chunk ${steps}/${TOTAL_STEPS}`,
          checkpoint,
        })
      } catch {
        // ignore transient update errors; next tick will retry
      }

      if (steps >= TOTAL_STEPS) {
        try {
          await getRepo().update(id, {
            status: 'completed',
            progress: 100,
            message: 'Import completed',
            completed_at: new Date().toISOString(),
          })
        } catch {
          // ignore
        } finally {
          clearInterval(interval)
          running.delete(id)
        }
      }
    }, TICK_MS)
  })()
}