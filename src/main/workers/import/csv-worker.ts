import fs from 'node:fs';
import { parse } from 'csv-parse';
import { z } from 'zod';
import {
  zCsvRow,
  normalizeCsvRow,
  type NormalizedRow,
  validateHeaders,
} from './csv-schemas';

export type RowError = {
  rowNo: number;
  reason: string;
  field?: string;
};

export type DuplicateInfo = {
  epicNumber: string;
  firstRowNo: number;
  dupRowNo: number;
};

export type Checkpoint = {
  importId: string;
  processed: number;
  errors: number;
  duplicates: number;
  lastRowNo: number;
  updatedAt: number;
};

export type ParseOptions = {
  importId: string;
  checkpointEvery?: number; // default 100
  onCheckpoint?: (cp: Checkpoint) => Promise<void> | void;
};

export type ParseResult = {
  importId: string;
  totalRows: number;
  validRows: number;
  errorCount: number;
  duplicateCount: number;
  duplicates: DuplicateInfo[];
  errors: RowError[];
  durationMs: number;
};

export async function parseCsvStream(filePath: string, opts: ParseOptions): Promise<ParseResult> {
  const start = Date.now();
  const { importId, checkpointEvery = 100, onCheckpoint } = opts;

  return new Promise<ParseResult>((resolve, reject) => {
    const stream = fs.createReadStream(filePath, { encoding: 'utf8' });
    const parser = parse({
      bom: true,
      skip_empty_lines: true,
      relax_column_count: true,
    });

    let headers: string[] | null = null;
    let rowNo = 0;
    let totalRows = 0;
    let validRows = 0;
    const errors: RowError[] = [];
    const duplicateMap = new Map<string, number>();
    const duplicates: DuplicateInfo[] = [];

    function handleCheckpoint() {
      if (!onCheckpoint) return;
      const cp: Checkpoint = {
        importId,
        processed: totalRows,
        errors: errors.length,
        duplicates: duplicates.length,
        lastRowNo: rowNo,
        updatedAt: Date.now(),
      };
      try {
        void onCheckpoint(cp);
      } catch {
        // swallow checkpoint side-effect errors (non-fatal)
      }
    }

    parser.on('readable', () => {
      let record: any[];
      // eslint-disable-next-line no-cond-assign
      while ((record = parser.read()) !== null) {
        if (!headers) {
          headers = record.map((s) => (s ?? '').toString().trim());
          const hv = validateHeaders(headers);
          if (!hv.ok) {
            errors.push({
              rowNo: 1,
              reason: `Missing required headers: ${hv.missing.join(', ')}`,
            });
            // We still continue but rows will likely fail further validation
          }
          continue;
        }

        rowNo += 1; // data rows start after header
        totalRows += 1;

        // Build row object
        const rowObj: Record<string, string> = {};
        headers.forEach((h, idx) => {
          rowObj[h] = (record[idx] ?? '').toString();
        });

        // Validate and normalize
        const parsed = zCsvRow.safeParse(rowObj);
        if (!parsed.success) {
          for (const issue of parsed.error.issues) {
            errors.push({
              rowNo: rowNo + 1, // +1 to account for header as line 1
              reason: issue.message,
              field: issue.path.join('.'),
            });
          }
          continue;
        }

        const normalized: NormalizedRow = normalizeCsvRow(parsed.data);

        // Duplicate detection (EPIC among this file)
        const epic = normalized.epicNumber;
        const first = duplicateMap.get(epic);
        if (first) {
          duplicates.push({
            epicNumber: epic,
            firstRowNo: first,
            dupRowNo: rowNo + 1, // file line including header
          });
        } else {
          duplicateMap.set(epic, rowNo + 1); // store 1-based file line number
        }

        validRows += 1;

        // Emit checkpoint
        if (checkpointEvery > 0 && totalRows % checkpointEvery === 0) {
          handleCheckpoint();
        }
      }
    });

    parser.on('error', (err) => {
      reject(err);
    });

    parser.on('end', () => {
      // final checkpoint
      handleCheckpoint();
      const durationMs = Date.now() - start;
      resolve({
        importId,
        totalRows,
        validRows,
        errorCount: errors.length,
        duplicateCount: duplicates.length,
        duplicates,
        errors,
        durationMs,
      });
    });

    stream.pipe(parser);
  });
}

// Convenience validator for a single row object (used by unit tests)
export function validateRow(raw: Record<string, unknown>): { ok: true; value: NormalizedRow } | { ok: false; issues: z.ZodIssue[] } {
  const r = zCsvRow.safeParse(raw);
  if (!r.success) {
    return { ok: false, issues: r.error.issues };
  }
  return { ok: true, value: normalizeCsvRow(r.data) };
}