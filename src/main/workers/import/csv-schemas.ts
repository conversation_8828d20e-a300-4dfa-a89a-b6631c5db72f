import { z } from 'zod';

// Helpers
export function extractPollingStation(raw: string) {
  const s = (raw || '').trim();
  const m = s.match(/^(\d+)\s*-\s*(.+)$/);
  if (!m) {
    return { pollingStationCode: null as number | null, pollingStationName: s || null };
  }
  return { pollingStationCode: Number(m[1]), pollingStationName: m[2].trim() };
}

export function extractSection(raw: string) {
  const s = (raw || '').trim();
  if (!s || s.toLowerCase() === 'unassigned') {
    return { sectionNumber: null as number | null, sectionName: s || 'Unassigned' };
  }
  const m = s.match(/^(\d+)\s*-\s*(.+)$/);
  if (!m) {
    return { sectionNumber: null as number | null, sectionName: s };
  }
  return { sectionNumber: Number(m[1]), sectionName: m[2].trim() };
}

export function normalizeGender(raw: string) {
  const s = (raw || '').trim().toLowerCase();
  if (s === 'male' || s === 'm') return 'M';
  if (s === 'female' || s === 'f') return 'F';
  return s.toUpperCase() || null;
}

const currentYear = new Date().getFullYear();
const minBirthYear = 1900;
const maxBirthYear = currentYear - 18;

export const zCsvRow = z.object({
  name: z.string().trim().min(1, 'name is required'),
  relation_type: z.string().trim().min(1, 'relation_type is required'),
  relation_name: z.string().trim().min(1, 'relation_name is required'),
  house_number: z.string().trim().min(1, 'house_number is required'),
  birth_year: z
    .string()
    .trim()
    .transform((v) => Number(v))
    .refine((n) => Number.isInteger(n), 'birth_year must be integer')
    .refine((n) => n >= minBirthYear && n <= maxBirthYear, `birth_year must be between ${minBirthYear} and ${maxBirthYear}`),
  gender: z
    .string()
    .transform((v) => normalizeGender(v))
    .refine((g) => g === 'M' || g === 'F', 'gender must be M or F'),
  epic_number: z
    .string()
    .trim()
    .toUpperCase()
    .regex(/^[A-Z]{3}[0-9]{7}$/, 'epic_number must match **********'),
  polling_station: z.string().trim().min(1, 'polling_station is required'),
  section: z.string().trim().min(1, 'section is required'),
});

export type CsvRow = z.infer<typeof zCsvRow>;

export type NormalizedRow = {
  name: string;
  relationType: string;
  relationName: string;
  houseNumber: string;
  birthYear: number;
  gender: 'M' | 'F';
  epicNumber: string;
  pollingStationCode: number | null;
  pollingStationName: string | null;
  sectionNumber: number | null;
  sectionName: string;
};

export function normalizeCsvRow(raw: CsvRow): NormalizedRow {
  const { pollingStationCode, pollingStationName } = extractPollingStation(raw.polling_station);
  const { sectionNumber, sectionName } = extractSection(raw.section);
  return {
    name: raw.name.trim(),
    relationType: raw.relation_type.trim(),
    relationName: raw.relation_name.trim(),
    houseNumber: raw.house_number.trim(),
    birthYear: raw.birth_year,
    // raw.gender has already been normalized/refined by zCsvRow to 'M' | 'F'
    gender: raw.gender as 'M' | 'F',
    epicNumber: raw.epic_number.trim().toUpperCase(),
    pollingStationCode,
    pollingStationName,
    sectionNumber,
    sectionName,
  };
}

export function validateHeaders(headers: string[]) {
  const required = [
    'name',
    'relation_type',
    'relation_name',
    'house_number',
    'birth_year',
    'gender',
    'epic_number',
    'polling_station',
    'section',
  ];
  const set = new Set(headers.map((h) => (h || '').trim()));
  const missing = required.filter((r) => !set.has(r));
  return { ok: missing.length === 0, missing };
}