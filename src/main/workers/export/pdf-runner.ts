import fs from 'fs';
import path from 'path';
import { sqlite } from '../../database/client';
import ExportJobRepository from '../../repositories/export-job-repository';
import { StubPdfGenerator } from './pdf-generator';

/**
 * startPdfExportJob
 * Enhanced PDF export with multi-page support, progress tracking, and cancellation handling.
 * Fetches up to 90 rows, splits into pages of 30, with progress updates at key checkpoints.
 */
export async function startPdfExportJob(args: {
  jobId: string;
  filters?: { pollingStationId?: string; sectionId?: string; status?: string } | null;
  outputDir?: string;
}) {
  const { jobId, filters, outputDir } = args;
  const repo = new ExportJobRepository();

  // Transition to running if not already
  try {
    await repo.update(jobId, {
      status: 'running',
      progress: 0,
      message: null,
    });
  } catch {
    // best-effort; continue
  }

  const where: string[] = ['deleted_at IS NULL'];
  const params: any[] = [];

  if (filters?.pollingStationId) {
    where.push('polling_station_id = ?');
    params.push(filters.pollingStationId);
  }
  if (filters?.sectionId) {
    where.push('section_id = ?');
    params.push(filters.sectionId);
  }
  if (filters?.status) {
    where.push('status = ?');
    params.push(filters.status);
  }

  const whereClause = where.length ? `WHERE ${where.join(' AND ')}` : '';
  const countStmt = sqlite.prepare(`SELECT COUNT(*) as c FROM voters ${whereClause}`);
  let totalRows = 0;
  try {
    const row = countStmt.get(...params) as { c: number } | undefined;
    totalRows = row?.c ?? 0;
  } catch (err: any) {
    await repo.update(jobId, {
      status: 'failed',
      progress: 0,
      message: `Count failed: ${err?.message ?? String(err)}`,
    });
    return;
  }

  // After COUNT: update progress to 10%
  await repo.update(jobId, {
    progress: 10,
    message: `Found ${totalRows} rows`,
  });

  // Check for cancellation after count
  const jobAfterCount = await repo.getById(jobId);
  if (!jobAfterCount) return;
  if (jobAfterCount.status === 'canceled') {
    await repo.update(jobId, { progress: Math.min(jobAfterCount.progress ?? 0, 100) });
    return;
  }

  // Prepare output path
  const outDir = outputDir ?? path.resolve(process.cwd(), 'tmp', 'exports');
  await ensureDir(outDir);
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileName = `export-${jobId}-${timestamp}.pdf`;
  const filePath = path.join(outDir, fileName);

  // Fetch up to 90 rows using filters, order by a stable key (id)
  const fetchStmt = sqlite.prepare(`
    SELECT id, name, epic_number, house_number, status
    FROM voters ${whereClause}
    ORDER BY id
    LIMIT 90
  `);

  let rows: any[] = [];
  try {
    rows = fetchStmt.all(...params) as any[];
  } catch (err: any) {
    await repo.update(jobId, {
      status: 'failed',
      progress: 10,
      message: `Data fetch failed: ${err?.message ?? String(err)}`,
    });
    return;
  }

  // Split into pages of 30
  const pages: any[][] = [];
  for (let i = 0; i < rows.length; i += 30) {
    pages.push(rows.slice(i, i + 30));
  }

  // Generate PDF with multi-page support
  const generator = new StubPdfGenerator();
  let currentProgress = 10;

  try {

    for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
      // Check for cancellation before each page
      const jobBeforePage = await repo.getById(jobId);
      if (!jobBeforePage) return;
      if (jobBeforePage.status === 'canceled') {
        // If canceled after any page write, persist partial PDF and mark job canceled
        if (pageIndex > 0) {
          await repo.update(jobId, {
            status: 'canceled',
            progress: currentProgress,
            result_path: filePath,
            message: 'Export canceled - partial PDF saved',
          } as any);
        } else {
          await repo.update(jobId, {
            status: 'canceled',
            progress: currentProgress,
            message: 'Export canceled',
          });
        }
        return;
      }

      const pageData = pages[pageIndex];
      const isFirstPage = pageIndex === 0;
      const isLastPage = pageIndex === pages.length - 1;

      await generator.generatePage(filePath, {
        jobId,
        timestamp: new Date().toISOString(),
        totalRows,
        pageNumber: pageIndex + 1,
        totalPages: pages.length,
        data: pageData,
        isFirstPage,
        isLastPage,
      });

      // Update progress at checkpoints
      if (pageIndex === 0) {
        currentProgress = 40; // Page 1 complete
      } else if (pageIndex === 1) {
        currentProgress = 70; // Page 2 complete
      } else if (isLastPage) {
        currentProgress = 100; // Final page complete
      }

      await repo.update(jobId, {
        progress: currentProgress,
        message: `Generated page ${pageIndex + 1}/${pages.length}`,
      });
    }

    // Finalize the PDF
    await generator.finalizePdf(filePath);

  } catch (err: any) {
    await repo.update(jobId, {
      status: 'failed',
      progress: currentProgress || 10,
      message: `PDF generation failed: ${err?.message ?? String(err)}`,
    });
    return;
  }

  // Final check for cancellation
  const jobAfterGen = await repo.getById(jobId);
  if (jobAfterGen?.status === 'canceled') {
    // Do not delete the file; just finalize canceled state
    await repo.update(jobId, {
      progress: Math.max(jobAfterGen.progress ?? 0, 100),
      result_path: filePath,
    } as any);
    return;
  }

  // Mark completed with resultPath and progress=100
  await repo.update(jobId, {
    status: 'completed',
    progress: 100,
    result_path: filePath,
    message: `PDF export completed: ${pages.length} pages, ${rows.length} rows`,
  } as any);
}

/**
 * Small helper to ensure directory exists
 */
async function ensureDir(p: string) {
  await fs.promises.mkdir(p, { recursive: true });
}