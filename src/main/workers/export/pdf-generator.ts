import fs from 'fs';
import path from 'path';

export interface PdfPageData {
  jobId: string;
  timestamp: string;
  totalRows: number;
  pageNumber: number;
  totalPages: number;
  data: any[];
  isFirstPage: boolean;
  isLastPage: boolean;
}

export interface PdfGenerator {
  generate(
    filePath: string,
    payload: { jobId: string; timestamp: string; totalRows: number; title?: string }
  ): Promise<void>;
  generatePage(filePath: string, pageData: PdfPageData): Promise<void>;
  finalizePdf(filePath: string): Promise<void>;
}

/**
 * StubPdfGenerator writes a deterministic, minimal single-page PDF containing simple text lines:
 * title, jobId, timestamp, totalRows.
 * No external deps; handcrafted PDF content adequate for tests. Starts with %PDF-1.4 and ends with %%EOF.
 */
export class StubPdfGenerator implements PdfGenerator {
  private pages: string[] = [];
  async generate(
    filePath: string,
    payload: { jobId: string; timestamp: string; totalRows: number; title?: string }
  ): Promise<void> {
    await fs.promises.mkdir(path.dirname(filePath), { recursive: true });

    const title = payload.title ?? 'Voters Export (stub)';
    const lines = [
      title,
      `jobId: ${payload.jobId}`,
      `timestamp: ${payload.timestamp}`,
      `totalRows: ${payload.totalRows}`,
    ];

    // Build a minimal PDF with one page and a single text stream.
    // Coordinates: start near top-left margin. Each line offset by 16pt.
    // Use a single font /F1 Helvetica 12pt.
    const header = '%PDF-1.4\n';
    const objects: string[] = [];

    // 1: Catalog
    // 2: Pages
    // 3: Page
    // 4: Font
    // 5: Content stream

    const obj1 = '1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n';
    const obj2 = '2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n';
    const obj4 = '4 0 obj\n<< /Type /Font /Subtype /Type1 /BaseFont /Helvetica >>\nendobj\n';

    // Build text stream content
    let text = 'BT\n/F1 12 Tf\n';
    // Start y near top (e.g., 750). PDF origin is bottom-left, so decrease y for each line
    const startY = 750;
    const lineHeight = 16;
    lines.forEach((line, idx) => {
      const y = startY - idx * lineHeight;
      // Escape parentheses and backslashes in text
      const escaped = line.replace(/([()\\])/g, '\\$1');
      text += `72 ${y} Td (${escaped}) Tj\n`;
    });
    text += 'ET';
    const streamContent = text;
    const streamLen = Buffer.byteLength(streamContent, 'utf8');

    const obj5 = `5 0 obj\n<< /Length ${streamLen} >>\nstream\n${streamContent}\nendstream\nendobj\n`;

    const obj3 =
      '3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Resources << /Font << /F1 4 0 R >> >> /Contents 5 0 R >>\nendobj\n';

    objects.push(obj1, obj2, obj3, obj4, obj5);

    // Calculate byte offsets
    let offset = header.length;
    const xrefPositions: number[] = [];
    const body = objects
      .map((obj) => {
        xrefPositions.push(offset);
        offset += obj.length;
        return obj;
      })
      .join('');

    const xrefStart = header.length + body.length;
    // xref table: object 0 is the head and unused
    let xref = 'xref\n0 6\n';
    xref += '0000000000 65535 f \n';
    for (let i = 0; i < xrefPositions.length; i++) {
      const pos = xrefPositions[i];
      xref += pos.toString().padStart(10, '0') + ' 00000 n \n';
    }

    const trailer =
      'trailer\n' +
      '<< /Size 6 /Root 1 0 R >>\n' +
      'startxref\n' +
      xrefStart +
      '\n%%EOF';

    const pdf = header + body + xref + trailer;

    // Write as binary-safe buffer to avoid encoding issues
    await fs.promises.writeFile(filePath, Buffer.from(pdf, 'utf8'));
  }

  async generatePage(filePath: string, pageData: PdfPageData): Promise<void> {
    await fs.promises.mkdir(path.dirname(filePath), { recursive: true });

    // Store page content for later finalization
    const lines = [
      `Page ${pageData.pageNumber}/${pageData.totalPages}`,
      `Job ID: ${pageData.jobId}`,
      `Timestamp: ${pageData.timestamp}`,
      `Total Rows: ${pageData.totalRows}`,
      `Page Data (${pageData.data.length} rows):`,
      ...pageData.data.map((row, idx) =>
        `${idx + 1}. ${row.name || 'N/A'} (${row.epic_number || 'N/A'}) - ${row.status || 'N/A'}`
      )
    ];

    this.pages.push(lines.join('\n'));

    // For first page, create the initial PDF structure
    if (pageData.isFirstPage) {
      const header = '%PDF-1.4\n';
      await fs.promises.writeFile(filePath, Buffer.from(header, 'utf8'));
    }
  }

  async finalizePdf(filePath: string): Promise<void> {
    // Create a complete PDF with all pages
    const title = 'Voters Export (Multi-page)';
    const allLines = [
      title,
      `Total Pages: ${this.pages.length}`,
      '',
      ...this.pages.flatMap((page, idx) => [`--- Page ${idx + 1} ---`, page, ''])
    ];

    // Build a minimal PDF with one page containing all content
    const header = '%PDF-1.4\n';
    const objects: string[] = [];

    const obj1 = '1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n';
    const obj2 = '2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n';
    const obj4 = '4 0 obj\n<< /Type /Font /Subtype /Type1 /BaseFont /Helvetica >>\nendobj\n';

    // Build text stream content
    let text = 'BT\n/F1 10 Tf\n';
    const startY = 750;
    const lineHeight = 12;
    allLines.forEach((line, idx) => {
      const y = startY - idx * lineHeight;
      if (y < 50) return; // Stop if we run out of page space
      const escaped = line.replace(/([()\\])/g, '\\$1');
      text += `72 ${y} Td (${escaped}) Tj\n`;
    });
    text += 'ET';
    const streamContent = text;
    const streamLen = Buffer.byteLength(streamContent, 'utf8');

    const obj5 = `5 0 obj\n<< /Length ${streamLen} >>\nstream\n${streamContent}\nendstream\nendobj\n`;
    const obj3 = '3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Resources << /Font << /F1 4 0 R >> >> /Contents 5 0 R >>\nendobj\n';

    objects.push(obj1, obj2, obj3, obj4, obj5);

    // Calculate byte offsets
    let offset = header.length;
    const xrefPositions: number[] = [];
    const body = objects
      .map((obj) => {
        xrefPositions.push(offset);
        offset += obj.length;
        return obj;
      })
      .join('');

    const xrefStart = header.length + body.length;
    let xref = 'xref\n0 6\n';
    xref += '0000000000 65535 f \n';
    for (let i = 0; i < xrefPositions.length; i++) {
      const pos = xrefPositions[i];
      xref += pos.toString().padStart(10, '0') + ' 00000 n \n';
    }

    const trailer =
      'trailer\n' +
      '<< /Size 6 /Root 1 0 R >>\n' +
      'startxref\n' +
      xrefStart +
      '\n%%EOF';

    const pdf = header + body + xref + trailer;
    await fs.promises.writeFile(filePath, Buffer.from(pdf, 'utf8'));

    // Reset for next use
    this.pages = [];
  }
}