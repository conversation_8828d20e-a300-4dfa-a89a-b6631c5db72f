/**
 * Export runner: streams CSV from voters with optional anonymization, supports cancellation,
 * periodic progress updates, and checkpointing. Mirrors the import runner pattern to avoid
 * circular deps by using global accessors when repository is not directly available.
 */
import { promises as fs } from 'node:fs'
import { join } from 'node:path'
import { sqlite } from '@/main/database/client'

type ExportJobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'canceled'
interface ExportJobCheckpoint {
  lastId?: string
  rows?: number
  bytes?: number
}
interface ExportJobPatch {
  status?: ExportJobStatus
  progress?: number
  message?: string | null
  result_path?: string | null
  error_report_path?: string | null
  checkpoint?: ExportJobCheckpoint | null
  updated_at?: string
  completed_at?: string | null
}

type ExportRepo = {
  update: (id: string, patch: ExportJobPatch) => Promise<void>
  getById: (id: string) => Promise<any | null>
}

declare global {
  // Provided by electron/main.ts similar to Import runner pattern
  // eslint-disable-next-line no-var
  var __getExportJobRepo: (() => ExportRepo | null) | undefined
  // eslint-disable-next-line no-var
  var __updateExportJob: ((id: string, patch: ExportJobPatch) => Promise<void>) | undefined
}

function nowIso() {
  return new Date().toISOString()
}

function clamp(n: number, min: number, max: number) {
  return Math.max(min, Math.min(max, n))
}

async function safeUpdate(jobId: string, patch: ExportJobPatch) {
  const repo = globalThis.__getExportJobRepo ? globalThis.__getExportJobRepo() : null
  if (repo && typeof repo.update === 'function') {
    await repo.update(jobId, patch)
    return
  }
  if (globalThis.__updateExportJob) {
    await globalThis.__updateExportJob(jobId, patch)
    return
  }
  // No-op if neither path is available (tests may stub)
}

/**
 * Simple anonymization: mask name and EPIC, keep other fields intact
 */
function anonymizeRow(row: any): any {
  const masked = { ...row }
  if (masked.name) {
    const n = String(masked.name)
    masked.name = n.length <= 2 ? '*'.repeat(n.length) : n[0] + '*'.repeat(Math.max(1, n.length - 2)) + n[n.length - 1]
  }
  if (masked.epic_number) {
    const e = String(masked.epic_number)
    const keep = Math.min(3, e.length)
    masked.epic_number = e.slice(0, keep) + '*'.repeat(Math.max(0, e.length - keep))
  }
  if (masked.house_number) {
    const h = String(masked.house_number)
    masked.house_number = h.replace(/[0-9]/g, '*')
  }
  return masked
}

function toCsvRow(row: any, headers: string[]): string {
  // naive CSV escaping for commas/quotes/newlines
  const esc = (v: any) => {
    if (v === null || v === undefined) return ''
    const s = String(v)
    if (/[",\n\r]/.test(s)) return '"' + s.replace(/"/g, '""') + '"'
    return s
  }
  return headers.map((h) => esc(row[h])).join(',')
}

async function ensureDirFor(filePath: string) {
  const dir = filePath.substring(0, filePath.lastIndexOf('/'))
  if (!dir) return
  await fs.mkdir(dir, { recursive: true }).catch(() => {})
}

export type StartExportParams = {
  jobId: string
  filters?: {
    pollingStationId?: string
    sectionId?: string
    status?: 'Active' | 'Expired' | 'Shifted' | 'Missing' | 'Duplicate' | 'Disqualified'
  } | null
  anonymize?: boolean
  outputDir?: string // optional target dir; defaults to app's userData or project temp under ./tmp
}

export async function startExportJob(params: StartExportParams) {
  const { jobId, filters = null, anonymize = false, outputDir } = params

  // Start mark
  await safeUpdate(jobId, {
    status: 'running',
    message: 'Starting export',
    updated_at: nowIso(),
  })

  try {
    // Decide output path
    const baseDir = outputDir || './tmp/exports'
    const fileName = `voters-${jobId}-${Date.now()}.csv`
    const targetPath = join(baseDir, fileName)
    await ensureDirFor(targetPath)

    const headers = [
      'id',
      'name',
      'relation_type',
      'relation_name',
      'house_number',
      'birth_year',
      'gender',
      'epic_number',
      'polling_station',
      'section',
      'status',
      'created_at',
      'updated_at',
    ]

    // Create file and write header
    await fs.writeFile(targetPath, headers.join(',') + '\n', 'utf8')
    let rowsWritten = 0
    let bytesWritten = (headers.join(',') + '\n').length

    // Paging variables
    const pageSize = 1000
    let lastId: string | undefined = undefined
    let canceled = false

    // Helper to check cancelation
    const checkCanceled = async () => {
      const repo = globalThis.__getExportJobRepo ? globalThis.__getExportJobRepo() : null
      const job = repo ? await repo.getById(jobId) : null
      if (job && job.status === 'canceled') {
        canceled = true
      }
    }

    // Estimate total for progress. If not available, we will progress based on rows (best effort).
    const totalRows = await countVoters(filters)

    while (true) {
      await checkCanceled()
      if (canceled) break

      const batch = await fetchVotersBatch({ afterId: lastId, limit: pageSize, filters })
      if (batch.length === 0) break

      const lines: string[] = []
      for (const row of batch) {
        const outRow = anonymize ? anonymizeRow(row) : row
        lines.push(toCsvRow(outRow, headers))
      }
      const chunk = lines.join('\n') + '\n'
      await fs.appendFile(targetPath, chunk, 'utf8')

      rowsWritten += batch.length
      bytesWritten += chunk.length
      lastId = batch[batch.length - 1].id

      // Update progress
      const progress = totalRows > 0 ? clamp((rowsWritten / totalRows) * 100, 0, 100) : clamp((rowsWritten % 1000) / 10, 0, 99.9)
      await safeUpdate(jobId, {
        progress,
        message: `Exported ${rowsWritten} rows`,
        checkpoint: { lastId, rows: rowsWritten, bytes: bytesWritten },
        updated_at: nowIso(),
      })
    }

    if (canceled) {
      await safeUpdate(jobId, {
        status: 'canceled',
        message: 'Export canceled',
        updated_at: nowIso(),
        completed_at: nowIso(),
      })
      return
    }

    await safeUpdate(jobId, {
      status: 'completed',
      progress: 100,
      message: `Completed export: ${rowsWritten} rows`,
      result_path: targetPath,
      updated_at: nowIso(),
      completed_at: nowIso(),
    })
  } catch (err: any) {
    await safeUpdate(jobId, {
      status: 'failed',
      message: `Export failed: ${err?.message || String(err)}`,
      updated_at: nowIso(),
      completed_at: nowIso(),
    })
  }
}

async function countVoters(filters?: {
  pollingStationId?: string
  sectionId?: string
  status?: 'Active' | 'Expired' | 'Shifted' | 'Missing' | 'Duplicate' | 'Disqualified'
} | null): Promise<number> {
  const where: string[] = [`deleted_at IS NULL`]
  const params: any[] = []
  if (filters?.pollingStationId) {
    where.push(`polling_station_id = ?`)
    params.push(filters.pollingStationId)
  }
  if (filters?.sectionId) {
    where.push(`section_id = ?`)
    params.push(filters.sectionId)
  }
  if (filters?.status) {
    where.push(`status = ?`)
    params.push(filters.status)
  }
  const sql = `SELECT COUNT(1) as c FROM voters WHERE ${where.join(' AND ')}`
  const row: any = sqlite.prepare(sql).get(...params)
  const c = row?.c ?? row?.C ?? 0
  return Number(c) || 0
}

async function fetchVotersBatch(args: {
  afterId?: string
  limit: number
  filters?: {
    pollingStationId?: string
    sectionId?: string
    status?: 'Active' | 'Expired' | 'Shifted' | 'Missing' | 'Duplicate' | 'Disqualified'
  } | null
}): Promise<any[]> {
  const { afterId, limit, filters } = args
  const where: string[] = [`deleted_at IS NULL`]
  const params: any[] = []
  if (afterId) {
    where.push(`id > ?`)
    params.push(afterId)
  }
  if (filters?.pollingStationId) {
    where.push(`polling_station_id = ?`)
    params.push(filters.pollingStationId)
  }
  if (filters?.sectionId) {
    where.push(`section_id = ?`)
    params.push(filters.sectionId)
  }
  if (filters?.status) {
    where.push(`status = ?`)
    params.push(filters.status)
  }

  const sql = `SELECT id,name,relation_type,relation_name,house_number,birth_year,gender,epic_number,polling_station,section,status,created_at,updated_at
               FROM voters
               WHERE ${where.join(' AND ')}
               ORDER BY id
               LIMIT ?`
  params.push(Math.max(1, Math.min(5000, limit)))
  const rows: any[] = sqlite.prepare(sql).all(...params)
  return rows || []
}

// naive SQL single-quote escape (no longer used due to parameterized queries)
function escapeSql(_s: string) {
  return _s
}