import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { execSync } from 'node:child_process';
import fs from 'node:fs';
import path from 'node:path';

// Paths
const DB_DIR = path.resolve(process.cwd(), 'data');
const DB_PATH = path.join(DB_DIR, 'app.db');

// Ensure data directory exists
if (!fs.existsSync(DB_DIR)) {
  fs.mkdirSync(DB_DIR, { recursive: true });
}

// Initialize DB connection
export const sqlite = new Database(DB_PATH);
export const db = drizzle(sqlite);

// Enable WAL and pragmas for performance
try {
  sqlite.pragma('journal_mode = WAL');
  sqlite.pragma('synchronous = NORMAL');
  sqlite.pragma('foreign_keys = ON');
} catch {
  // ignore
}

// Helper to run drizzle-kit migrations programmatically (optional)
export function runMigrations(): void {
  try {
    execSync('pnpm drizzle-kit migrate', { stdio: 'inherit' });
  } catch {
    // Allow tests to handle if migrations are not yet generated
  }
}