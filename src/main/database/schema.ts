import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

// Utility: ULID surrogate type (stored as TEXT)
export const ulid = () => text().notNull();

// Timestamps helpers (ISO strings)
const createdAt = () => text('created_at').notNull();
const updatedAt = () => text('updated_at').notNull();
const deletedAt = () => text('deleted_at');

// polling_stations
export const pollingStations = sqliteTable('polling_stations', {
  id: text('id').primaryKey().notNull(), // ULID
  name: text('name').notNull(),
  code: text('code').notNull(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
});

// sections
export const sections = sqliteTable('sections', {
  id: text('id').primaryKey().notNull(), // ULID
  pollingStationId: text('polling_station_id').notNull().references(() => pollingStations.id),
  name: text('name').notNull(),
  code: text('code').notNull(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
});

// voters
export const voters = sqliteTable('voters', {
  id: text('id').primaryKey().notNull(), // ULID
  name: text('name').notNull(),
  relationshipType: text('relationship_type').notNull(), // Father/Mother/Husband/Others
  relationshipName: text('relationship_name').notNull(),
  gender: text('gender').notNull(), // Male/Female/Other
  birthYear: integer('birth_year'),
  epicNumber: text('epic_number').notNull(),
  houseNumber: text('house_number').notNull(),
  pollingStationId: text('polling_station_id').notNull().references(() => pollingStations.id),
  sectionId: text('section_id').references(() => sections.id),
  phone: text('phone'),
  email: text('email'),
  facebook: text('facebook'),
  instagram: text('instagram'),
  twitter: text('twitter'),
  status: text('status').notNull(), // Active/Expired/Shifted/Missing/Duplicate/Disqualified
  supporterStatus: text('supporter_status'),
  education: text('education'),
  occupation: text('occupation'),
  community: text('community'),
  religion: text('religion'),
  economicStatus: text('economic_status'),
  customNotes: text('custom_notes'),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
});

// voter_turnout
export const voterTurnout = sqliteTable('voter_turnout', {
  id: text('id').primaryKey().notNull(), // ULID
  voterId: text('voter_id').notNull().references(() => voters.id),
  electionYear: integer('election_year').notNull(),
  voted: integer('voted', { mode: 'boolean' }).notNull(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
});

// transactions
export const transactions = sqliteTable('transactions', {
  id: text('id').primaryKey().notNull(), // ULID
  voterId: text('voter_id').notNull().references(() => voters.id),
  transactionDate: text('transaction_date').notNull(), // YYYY-MM-DD
  purpose: text('purpose').notNull(),
  amount: integer('amount').notNull(), // integer only
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
});

// config_options
export const configOptions = sqliteTable('config_options', {
  id: text('id').primaryKey().notNull(), // ULID
  category: text('category').notNull(),
  value: text('value').notNull(),
  displayName: text('display_name').notNull(),
  displayOrder: integer('display_order').notNull(),
  active: integer('active', { mode: 'boolean' }).notNull(),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
});

// users
export const users = sqliteTable('users', {
  id: text('id').primaryKey().notNull(), // ULID
  username: text('username').notNull(),
  passwordHash: text('password_hash').notNull(),
  role: text('role').notNull(), // owner/admin/editor/viewer
  fullName: text('full_name'),
  active: integer('active', { mode: 'boolean' }).notNull(),
  lastLogin: text('last_login'),
  sessionExpires: text('session_expires'),
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  deletedAt: deletedAt(),
});

// import_jobs (persistent background jobs for Import workers)
export const importJobs = sqliteTable('import_jobs', {
  id: text('id').primaryKey().notNull(), // ULID
  kind: text('kind').notNull(), // 'import'
  status: text('status').notNull(), // pending|running|completed|failed|canceled
  progress: integer('progress').notNull(), // 0..100
  counters: text('counters').notNull(), // JSON stringified { processed, accepted, rejected }
  filePath: text('file_path').notNull(),
  mapping: text('mapping').notNull(), // JSON stringified mapping
  options: text('options').notNull(), // JSON stringified options
  message: text('message'),
  errorReportPath: text('error_report_path'),
  checkpoint: text('checkpoint'), // JSON stringified checkpoint/resume data
  createdAt: createdAt(),
  updatedAt: updatedAt(),
  completedAt: text('completed_at'),
  deletedAt: deletedAt(),
});

// Note: Unique/partial indexes, FTS5 virtual table, and triggers will be created via migrations
// to support soft-delete-aware uniqueness and FTS maintenance.