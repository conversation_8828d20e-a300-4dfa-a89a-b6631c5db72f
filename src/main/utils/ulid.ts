/**
 * ULID (Universally Unique Lexicographically Sortable Identifier) generator
 * Provides unique, sortable IDs for offline-first applications
 */

/**
 * Generate a ULID timestamp (48 bits)
 * @returns ULID timestamp
 */
export function generateTimestamp(): number {
  const now = Date.now();
  // Convert to milliseconds and encode as 48-bit integer
  return now * 1000 + Math.floor(Math.random() * 1000);
}

/**
 * Generate a random component (80 bits)
 * @returns Random component as string
 */
export function generateRandom(): string {
  const chars = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
  let result = '';

  // Generate 80 bits of randomness (13 characters of 6 bits each)
  for (let i = 0; i < 13; i++) {
    const randomValue = Math.floor(Math.random() * 32);
    result += chars[randomValue % chars.length];
  }

  return result;
}

/**
 * Generate a complete ULID
 * @returns ULID string
 */
export function generateId(): string {
  const timestamp = generateTimestamp().toString(36).padStart(10, '0');
  const random = generateRandom();
  return timestamp + random;
}

/**
 * Parse ULID into timestamp and random components
 * @param ulid - ULID string to parse
 * @returns Parsed components
 */
export function parseUlid(ulid: string): { timestamp: number; random: string } {
  if (ulid.length !== 26) {
    throw new Error('Invalid ULID length');
  }

  const timestamp = parseInt(ulid.substring(0, 10), 36);
  const random = ulid.substring(10);

  return { timestamp, random };
}

/**
 * Check if a string is a valid ULID
 * @param ulid - String to validate
 * @returns True if valid ULID
 */
export function isValidUlid(ulid: string): boolean {
  return /^[0-9a-hjkmnp-tv-z]{26}$/i.test(ulid);
}