import { DomainError, ErrorCode } from '@/shared/types/errors'

export type TurnoutRecord = {
  id: string
  voterId: string
  electionYear: number
  voted: boolean
  notes?: string | null
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
}

export type TurnoutDTO = {
  id: string
  voterId: string
  electionYear: number
  voted: boolean
  notes?: string | null
  createdAt: string
  updatedAt: string
}

export type DemographicDimension =
  | 'gender'
  | 'education'
  | 'occupation'
  | 'community'
  | 'religion'
  | 'economic_status'
  | 'supporter_status'

export interface TurnoutRepository {
  findById(id: string): Promise<TurnoutRecord | null>
  find(voterId: string, electionYear: number): Promise<TurnoutRecord | null>
  upsert(input: { voterId: string; electionYear: number; voted: boolean; notes?: string | null }): Promise<TurnoutRecord>
  delete(id: string): Promise<void>
  listByVoter(voterId: string): Promise<TurnoutRecord[]>
  aggregateByStation(electionYear: number): Promise<Array<{ pollingStationId: string; total: number; voted: number }>>
  aggregateBySection(
    electionYear: number,
    pollingStationId?: string
  ): Promise<Array<{ sectionId: string | null; total: number; voted: number }>>
  aggregateByDemographic(
    electionYear: number,
    dimension: DemographicDimension
  ): Promise<Array<{ key: string; total: number; voted: number }>>
}

export interface VoterRepositoryForTurnout {
  findById(id: string): Promise<
    | {
        id: string
        polling_station_id: string
        section_id: string | null
        status: string
        gender?: string | null
        education?: string | null
        occupation?: string | null
        community?: string | null
        religion?: string | null
        economic_status?: string | null
        supporter_status?: string | null
        deleted_at?: string | null
      }
    | null
  >
}

function toDto(r: TurnoutRecord): TurnoutDTO {
  return {
    id: r.id,
    voterId: r.voterId,
    electionYear: r.electionYear,
    voted: r.voted,
    notes: r.notes ?? null,
    createdAt: r.createdAt,
    updatedAt: r.updatedAt,
  }
}

function validateElectionYear(year: number) {
  const min = 2000
  const max = new Date().getFullYear() + 1
  if (!Number.isInteger(year) || year < min || year > max) {
    throw new DomainError(ErrorCode.VALIDATION_ERROR, 'Invalid election year', { min, max, year })
  }
}

function validateNotes(notes?: string | null) {
  if (notes && notes.length > 1000) {
    throw new DomainError(ErrorCode.VALIDATION_ERROR, 'Notes too long', { max: 1000 })
  }
}

export class TurnoutService {
  constructor(
    private readonly repo: TurnoutRepository,
    private readonly voterRepo: VoterRepositoryForTurnout
  ) {}

  async recordTurnout(input: { voterId: string; electionYear: number; voted: boolean; notes?: string | null }) {
    try {
      const voterId = (input.voterId ?? '').trim()
      if (!voterId) {
        throw new DomainError(ErrorCode.VALIDATION_ERROR, 'voterId is required')
      }
      validateElectionYear(input.electionYear)
      validateNotes(input.notes)

      // Ensure voter exists and is not soft-deleted
      const voter = await this.voterRepo.findById(voterId)
      if (!voter || voter.deleted_at) {
        throw new DomainError(ErrorCode.NOT_FOUND, 'Voter not found', { voterId })
      }

      const saved = await this.repo.upsert({
        voterId,
        electionYear: input.electionYear,
        voted: !!input.voted,
        notes: input.notes ?? null,
      })
      return toDto(saved)
    } catch (err) {
      if (err instanceof DomainError) throw err
      throw DomainError.from(err)
    }
  }

  async getTurnoutByVoterYear(voterId: string, electionYear: number) {
    try {
      const vid = (voterId ?? '').trim()
      if (!vid) {
        throw new DomainError(ErrorCode.VALIDATION_ERROR, 'voterId is required')
      }
      validateElectionYear(electionYear)
      const rec = await this.repo.find(vid, electionYear)
      return rec ? toDto(rec) : null
    } catch (err) {
      if (err instanceof DomainError) throw err
      throw DomainError.from(err)
    }
  }

  async getVoterTurnoutHistory(voterId: string) {
    try {
      const vid = (voterId ?? '').trim()
      if (!vid) {
        throw new DomainError(ErrorCode.VALIDATION_ERROR, 'voterId is required')
      }
      const list = await this.repo.listByVoter(vid)
      return list.map(toDto)
    } catch (err) {
      if (err instanceof DomainError) throw err
      throw DomainError.from(err)
    }
  }

  async deleteTurnout(id: string) {
    try {
      const tid = (id ?? '').trim()
      if (!tid) {
        throw new DomainError(ErrorCode.VALIDATION_ERROR, 'turnout id is required')
      }
      // ensure exists first for NOT_FOUND semantics
      const existing = await this.repo.findById(tid)
      if (!existing) {
        throw new DomainError(ErrorCode.NOT_FOUND, 'Turnout record not found', { id })
      }
      await this.repo.delete(tid)
    } catch (err) {
      if (err instanceof DomainError) throw err
      throw DomainError.from(err)
    }
  }

  async getStationReport(electionYear: number) {
    try {
      validateElectionYear(electionYear)
      const rows = await this.repo.aggregateByStation(electionYear)
      return rows.map((r) => ({
        pollingStationId: r.pollingStationId,
        total: r.total,
        voted: r.voted,
        turnoutPct: r.total > 0 ? (r.voted / r.total) * 100 : 0,
      }))
    } catch (err) {
      if (err instanceof DomainError) throw err
      throw DomainError.from(err)
    }
  }

  async getSectionReport(electionYear: number, pollingStationId?: string) {
    try {
      validateElectionYear(electionYear)
      const rows = await this.repo.aggregateBySection(electionYear, pollingStationId)
      return rows.map((r) => ({
        sectionId: r.sectionId,
        total: r.total,
        voted: r.voted,
        turnoutPct: r.total > 0 ? (r.voted / r.total) * 100 : 0,
      }))
    } catch (err) {
      if (err instanceof DomainError) throw err
      throw DomainError.from(err)
    }
  }

  async getDemographicReport(electionYear: number, dimension: DemographicDimension) {
    try {
      validateElectionYear(electionYear)
      const rows = await this.repo.aggregateByDemographic(electionYear, dimension)
      return rows.map((r) => ({
        key: r.key,
        total: r.total,
        voted: r.voted,
        turnoutPct: r.total > 0 ? (r.voted / r.total) * 100 : 0,
      }))
    } catch (err) {
      if (err instanceof DomainError) throw err
      throw DomainError.from(err)
    }
  }
}

export default TurnoutService