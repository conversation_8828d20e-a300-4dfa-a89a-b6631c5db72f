import { VoterRepository } from '../repositories/voter-repository';
import { DomainError, ErrorCode } from '../../shared/types/errors';
import { VoterInput, VoterUpdate, VoterSearchFilters } from '../../shared/types/schemas';
import { ulid } from 'ulid';

export class VoterService {
  constructor(private voterRepository: VoterRepository) {}

  /**
   * Create a new voter with validation
   */
  async createVoter(voterData: VoterInput): Promise<any> {
    // Validate EPIC number format
    this.validateEpicNumber(voterData.epic_number);

    // Validate required fields
    this.validateRequiredFields(voterData);

    // Validate birth year if provided
    if (voterData.birth_year) {
      this.validateBirthYear(voterData.birth_year);
    }

    // Check for duplicate EPIC number
    const existingVoter = await this.voterRepository.findByEpicNumber(voterData.epic_number);
    if (existingVoter) {
      throw new DomainError(
        ErrorCode.CONFLICT,
        'EPIC number already exists',
        { epicNumber: voterData.epic_number }
      );
    }

    // Validate required fields before create (including relationship_name when relationship_type provided)
    this.validateRequiredFields(voterData);
    if (voterData.relationship_type && !voterData.relationship_name) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        'relationship_name is required when relationship_type is provided',
        { missingFields: ['relationship_name'] }
      );
    }

    // Create voter with timestamps
    const voterToCreate = {
      ...voterData,
      id: ulid(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    try {
      return await this.voterRepository.create(voterToCreate);
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Update an existing voter with validation
   */
  async updateVoter(id: string, updateData: VoterUpdate): Promise<any> {
    // Find existing voter
    const existingVoter = await this.voterRepository.findById(id);
    if (!existingVoter) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Voter not found',
        { voterId: id }
      );
    }

    // Validate EPIC number format if provided
    if (updateData.epic_number) {
      this.validateEpicNumber(updateData.epic_number);

      // Check for duplicate EPIC number (excluding current voter)
      const existingWithEpic = await this.voterRepository.findByEpicNumber(updateData.epic_number);
      if (existingWithEpic && existingWithEpic.id !== id) {
        throw new DomainError(
          ErrorCode.CONFLICT,
          'EPIC number already exists',
          { epicNumber: updateData.epic_number }
        );
      }
    }

    // Validate birth year if provided
    if (updateData.birth_year) {
      this.validateBirthYear(updateData.birth_year);
    }

    // Validate required fields if name or polling_station_id is being updated
    if (updateData.name || updateData.polling_station_id) {
      const updatedData = { ...existingVoter, ...updateData };
      // Validate only presence of required fields without strict typing to avoid enum widening issues
      this.validateRequiredFields(updatedData as any);
    }

    // Update with timestamp
    const dataToUpdate = {
      ...updateData,
      updated_at: new Date().toISOString(),
    };

    return this.voterRepository.update(id, dataToUpdate);
  }

  /**
   * Delete a voter (soft delete for Duplicate/Disqualified, soft delete for others)
   */
  async deleteVoter(id: string): Promise<void> {
    // Find existing voter
    const existingVoter = await this.voterRepository.findById(id);
    if (!existingVoter) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Voter not found',
        { voterId: id }
      );
    }

    // For Duplicate/Disqualified status, always soft delete
    if (existingVoter.status === 'Duplicate' || existingVoter.status === 'Disqualified') {
      await this.voterRepository.softDelete(id);
      return;
    }

    // For other statuses, soft delete as well
    await this.voterRepository.softDelete(id);
  }

  /**
   * Get voter by ID
   */
  async getVoterById(id: string): Promise<any> {
    const voter = await this.voterRepository.findById(id);
    if (!voter) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Voter not found',
        { voterId: id }
      );
    }
    return voter;
  }

  /**
   * Get voters with filters
   */
  async getVoters(filters?: VoterSearchFilters): Promise<any[]> {
    return this.voterRepository.findMany(filters);
  }

  /**
   * Search voters with full-text search
   */
  async searchVoters(query: string, filters?: VoterSearchFilters): Promise<any[]> {
    return this.voterRepository.search(query, filters);
  }

  /**
   * Get voters by polling station
   */
  async getVotersByPollingStation(pollingStationId: string, filters?: VoterSearchFilters): Promise<any[]> {
    return this.voterRepository.findByPollingStation(pollingStationId, filters);
  }

  /**
   * Get voters by section
   */
  async getVotersBySection(sectionId: string): Promise<any[]> {
    // Handle "unassigned" section
    if (sectionId === 'unassigned') {
      return this.voterRepository.findBySection(sectionId);
    }
    return this.voterRepository.findBySection(sectionId);
  }

  /**
   * Get voters by status
   */
  async getVotersByStatus(status: string): Promise<any[]> {
    return this.voterRepository.findByStatus(status);
  }

  /**
   * Get voter statistics
   */
  async getVoterStatistics(): Promise<any> {
    return this.voterRepository.getStatistics();
  }

  /**
   * Count voters with filters
   */
  async countVoters(filters?: VoterSearchFilters): Promise<number> {
    return this.voterRepository.countWithFilters(filters);
  }

  /**
   * Validate EPIC number format (3 uppercase letters + 7 digits)
   */
  private validateEpicNumber(epicNumber: string): void {
    const epicRegex = /^[A-Z]{3}\d{7}$/;
    if (!epicRegex.test(epicNumber)) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid EPIC number format. Must be 3 uppercase letters followed by 7 digits (e.g., **********)',
        { epicNumber }
      );
    }
  }

  /**
   * Validate required fields
   * Accepts a generic record to avoid type incompatibilities when merging DB and update payloads.
   */
  private validateRequiredFields(voterData: Record<string, unknown>): void {
    const requiredFields = ['name', 'polling_station_id', 'relationship_type', 'status', 'epic_number', 'house_number', 'gender'];
    const missingFields = requiredFields.filter((field) => {
      const val = voterData[field];
      return val === undefined || val === null || (typeof val === 'string' && val.trim().length === 0);
    });

    if (missingFields.length > 0) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        `Missing required fields: ${missingFields.join(', ')}`,
        { missingFields }
      );
    }
  }

  /**
   * Validate birth year range (1900 to current year - 18)
   */
  private validateBirthYear(birthYear: number): void {
    const currentYear = new Date().getFullYear();
    const minYear = 1900;
    const maxYear = currentYear - 18;

    if (birthYear < minYear || birthYear > maxYear) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        `Birth year must be between ${minYear} and ${maxYear}`,
        { birthYear, minYear, maxYear }
      );
    }
  }
}