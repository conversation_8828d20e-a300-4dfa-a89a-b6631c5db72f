import { DomainError, ErrorCode } from '../../shared/types/errors';

type Pagination = { limit?: number; offset?: number };
type Category = {
  id: string;
  name: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
};
type ValueRow = {
  id: string;
  category_id: string;
  label: string;
  display_order: number;
  active: boolean;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
};

type ConfigRepository = {
  categories: {
    findMany: (args?: any) => Promise<Category[]>;
    findUnique: (id: string) => Promise<Category | null>;
    create: (data: Partial<Category>) => Promise<Category>;
    update: (id: string, data: Partial<Category>) => Promise<Category>;
  };
  values: {
    findMany: (args?: any) => Promise<ValueRow[]>;
    findUnique: (id: string) => Promise<ValueRow | null>;
    create: (data: Partial<ValueRow>) => Promise<ValueRow>;
    update: (id: string, data: Partial<ValueRow>) => Promise<ValueRow>;
    softDelete: (id: string) => Promise<ValueRow>;
  };
  helpers: {
    countValueUsages: (valueId: string) => Promise<number>;
  };
};

export class ConfigService {
  constructor(private readonly repo: ConfigRepository) {}

  // Categories
  async createCategory(name: string): Promise<Category> {
    const trimmed = (name ?? '').trim();
    if (!trimmed) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        'Category name is required',
        { name }
      );
    }
    // case-insensitive uniqueness among non-deleted
    const existing = await this.repo.categories.findMany({});
    const conflict = existing.find(
      (c) => !c.deleted_at && c.name.toLowerCase() === trimmed.toLowerCase()
    );
    if (conflict) {
      throw new DomainError(
        ErrorCode.CONFLICT,
        'CONFLICT: Category name already exists',
        { name }
      );
    }
    const now = new Date();
    return this.repo.categories.create({
      name: trimmed,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    });
  }

  async renameCategory(id: string, newName: string): Promise<Category> {
    const trimmed = (newName ?? '').trim();
    if (!trimmed) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        'Category name is required',
        { newName }
      );
    }
    const cat = await this.repo.categories.findUnique(id);
    if (!cat) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Category not found',
        { id }
      );
    }
    const existing = await this.repo.categories.findMany({});
    const conflict = existing.find(
      (c) =>
        !c.deleted_at &&
        c.id !== id &&
        c.name.toLowerCase() === trimmed.toLowerCase()
    );
    if (conflict) {
      throw new DomainError(
        ErrorCode.CONFLICT,
        'CONFLICT: Category name already exists',
        { id, newName }
      );
    }
    return this.repo.categories.update(id, { name: trimmed, updated_at: new Date() });
  }

  async listCategories(pagination?: Pagination): Promise<Array<{ id: string; name: string; counts: { active: number; inactive: number; total: number } }>> {
    const paged = this.sanitizePagination(pagination);
    const cats = await this.repo.categories.findMany(paged);
    const results: Array<{ id: string; name: string; counts: { active: number; inactive: number; total: number } }> = [];
    for (const c of cats) {
      const values = await this.repo.values.findMany({ where: { category_id: c.id } });
      const active = values.filter(v => v.active && !v.deleted_at).length;
      const total = values.filter(v => !v.deleted_at).length;
      const inactive = total - active;
      results.push({
        id: c.id,
        name: c.name,
        counts: { active, inactive, total },
      });
    }
    return results;
  }

  // Values
  async addValue(categoryId: string, label: string, displayOrder?: number): Promise<ValueRow> {
    const trimmed = (label ?? '').trim();
    if (!trimmed) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        'Value label is required',
        { label }
      );
    }
    const cat = await this.repo.categories.findUnique(categoryId);
    if (!cat) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Category not found',
        { categoryId }
      );
    }
    const existing = await this.repo.values.findMany({ where: { category_id: categoryId } });
    const dup = existing.find(
      (v) => !v.deleted_at && v.label.toLowerCase() === trimmed.toLowerCase()
    );
    if (dup) {
      throw new DomainError(
        ErrorCode.CONFLICT,
        'CONFLICT: Duplicate value within category',
        { categoryId, label }
      );
    }
    let order = displayOrder;
    if (order === undefined || order === null || Number.isNaN(Number(order))) {
      const maxOrder =
        existing.reduce((max, v) => Math.max(max, Number(v.display_order) || 0), 0) || 0;
      order = maxOrder + 1;
    }
    const now = new Date();
    return this.repo.values.create({
      category_id: categoryId,
      label: trimmed,
      display_order: order,
      active: true,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    });
  }

  async listValues(categoryId: string, pagination?: Pagination): Promise<ValueRow[]> {
    const cat = await this.repo.categories.findUnique(categoryId);
    if (!cat) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Category not found',
        { categoryId }
      );
    }
    const paged = this.sanitizePagination(pagination);
    const values = await this.repo.values.findMany({ where: { category_id: categoryId, ...paged } });
    // Order by display_order asc; if equal, active first
    return [...values].sort((a, b) => {
      if (a.display_order !== b.display_order) {
        return (a.display_order ?? 0) - (b.display_order ?? 0);
      }
      if (a.active === b.active) return 0;
      return a.active ? -1 : 1;
    });
  }

  async updateValue(id: string, data: { label?: string; active?: boolean; display_order?: number }): Promise<ValueRow> {
    const row = await this.repo.values.findUnique(id);
    if (!row) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Value not found',
        { id }
      );
    }
    const patch: Partial<ValueRow> = {};
    if (data.label !== undefined) {
      const trimmed = (data.label ?? '').trim();
      if (!trimmed) {
        throw new DomainError(
          ErrorCode.VALIDATION_ERROR,
          'Value label is required',
          { label: data.label }
        );
      }
      // uniqueness in category
      const siblings = await this.repo.values.findMany({ where: { category_id: row.category_id } });
      const dup = siblings.find(
        (v) =>
          !v.deleted_at &&
          v.id !== id &&
          v.label.toLowerCase() === trimmed.toLowerCase()
      );
      if (dup) {
        throw new DomainError(
          ErrorCode.CONFLICT,
          'CONFLICT: Duplicate value within category',
          { id, label: data.label }
        );
      }
      patch.label = trimmed;
    }
    if (data.active !== undefined) {
      patch.active = !!data.active;
    }
    if (data.display_order !== undefined) {
      const n = Number(data.display_order);
      patch.display_order = Number.isNaN(n) ? row.display_order : n;
    }
    patch.updated_at = new Date();
    return this.repo.values.update(id, patch);
  }

  async removeValue(id: string, opts?: { hard?: boolean }): Promise<ValueRow> {
    const row = await this.repo.values.findUnique(id);
    if (!row) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Value not found',
        { id }
      );
    }
    const usages = await this.repo.helpers.countValueUsages(id);
    if (opts?.hard) {
      if (usages > 0) {
        throw new DomainError(
          ErrorCode.CONFLICT,
          'CONFLICT: Value is in use and cannot be hard-deleted',
          { id, usages }
        );
      }
      // For simplicity in this mock-driven service layer, reuse softDelete to represent deletion
      return this.repo.values.softDelete(id);
    }
    return this.repo.values.softDelete(id);
  }

  async deactivateValue(id: string): Promise<ValueRow> {
    const row = await this.repo.values.findUnique(id);
    if (!row) {
      throw new DomainError(
        ErrorCode.NOT_FOUND,
        'Value not found',
        { id }
      );
    }
    return this.repo.values.update(id, { active: false, updated_at: new Date() });
  }

  // Helpers
  private sanitizePagination<T extends Record<string, any> | undefined>(filters: T): T {
    if (!filters) return filters as T;
    const out: any = { ...filters };
    if (out.limit !== undefined) {
      out.limit = Math.min(Math.max(Number(out.limit) || 0, 1), 100);
    }
    if (out.offset !== undefined) {
      out.offset = Math.max(Number(out.offset) || 0, 0);
    }
    return out as T;
  }
}