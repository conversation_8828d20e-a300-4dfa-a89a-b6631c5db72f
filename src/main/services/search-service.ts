import { DomainError, ErrorCode } from '../../shared/types/errors';
import { VoterSearchFilters } from '../../shared/types/schemas';

export class SearchService {
  constructor(private readonly voterRepository: {
    search: (query: string, filters?: any) => Promise<any[]>;
    findMany: (filters?: any) => Promise<any[]>;
    countWithFilters: (filters?: any) => Promise<number>;
  }) {}

  async search(query: string, filters?: VoterSearchFilters): Promise<any[]> {
    const trimmed = (query ?? '').trim();
    if (!trimmed) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        'Search query is required and cannot be empty',
        { query }
      );
    }

    const safeFilters = this.sanitizePagination(filters);
    return this.voterRepository.search(trimmed, safeFilters);
  }

  async list(filters?: VoterSearchFilters): Promise<any[]> {
    const safeFilters = this.sanitizePagination(filters);
    return this.voterRepository.findMany(safeFilters);
  }

  async count(filters?: VoterSearchFilters): Promise<number> {
    const safeFilters = this.sanitizePagination(filters);
    return this.voterRepository.countWithFilters(safeFilters);
  }

  private sanitizePagination<T extends Record<string, any> | undefined>(filters: T): T {
    if (!filters) return filters as T;

    const out: any = { ...filters };
    if (out.limit !== undefined) {
      out.limit = Math.min(Math.max(Number(out.limit) || 0, 1), 100);
    }
    if (out.offset !== undefined) {
      out.offset = Math.max(Number(out.offset) || 0, 0);
    }
    return out as T;
  }
}