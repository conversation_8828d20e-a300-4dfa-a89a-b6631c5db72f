import argon2 from "argon2";
import { DomainError, ErrorCode } from "@/shared/types/errors";
import { generateId } from "@/main/utils/ulid";

// Types aligned with tests and minimal contract
export type UserRole = "owner" | "admin" | "editor" | "viewer";

export type UserRecord = {
  id: string;
  username: string;
  role: UserRole;
  passwordHash: string;
  active: boolean;
  deletedAt: number | null;
  createdAt: number;
  updatedAt: number;
};

export type UserDTO = {
  id: string;
  username: string;
  role: UserRole;
  active: boolean;
};

export interface UserRepository {
  findById(id: string): Promise<UserRecord | null>;
  findByUsername(username: string, opts?: { includeDeleted?: boolean }): Promise<UserRecord | null>;
  create(data: {
    id: string;
    username: string;
    role: UserRole;
    passwordHash: string;
    active: boolean;
    createdAt: number;
    updatedAt: number;
    deletedAt: null;
  }): Promise<UserRecord>;
  update(
    id: string,
    data: Partial<Pick<UserRecord, "username" | "role" | "passwordHash" | "active" | "deletedAt" | "updatedAt">>
  ): Promise<UserRecord>;
}

function toDto(u: UserRecord): UserDTO {
  return { id: u.id, username: u.username, role: u.role, active: u.active };
}

function isValidPassword(pw: string): boolean {
  if (pw.length < 8) return false;
  const hasLetter = /[A-Za-z]/.test(pw);
  const hasNumber = /[0-9]/.test(pw);
  return hasLetter && hasNumber;
}

function assertRole(role: unknown): role is UserRole {
  return role === "owner" || role === "admin" || role === "editor" || role === "viewer";
}

export class AuthService {
  constructor(private readonly repo: UserRepository) {}

  async createUser(input: { username: string; password: string; role?: UserRole }): Promise<UserDTO> {
    try {
      const username = (input.username ?? "").trim();
      const password = input.password ?? "";
      const role: UserRole = input.role ?? "viewer";

      if (!username) {
        throw new DomainError(ErrorCode.VALIDATION_ERROR, "Username is required");
      }
      if (!assertRole(role)) {
        throw new DomainError(ErrorCode.VALIDATION_ERROR, "Invalid role");
      }
      if (!isValidPassword(password)) {
        throw new DomainError(ErrorCode.VALIDATION_ERROR, "Invalid password policy");
      }

      // Uniqueness is case-sensitive and soft-deleted users block reuse
      const existing = await this.repo.findByUsername(username, { includeDeleted: true });
      if (existing) {
        throw new DomainError(ErrorCode.CONFLICT, "Username already exists");
      }

      const id = generateId();
      const now = Date.now();
      const passwordHash = await argon2.hash(password);

      const created = await this.repo.create({
        id,
        username,
        role,
        passwordHash,
        active: true,
        createdAt: now,
        updatedAt: now,
        deletedAt: null,
      });
      return toDto(created);
    } catch (err) {
      if (err instanceof DomainError) throw err;
      throw DomainError.from(err);
    }
  }

  async authenticate(usernameRaw: string, password: string): Promise<UserDTO> {
    try {
      const username = (usernameRaw ?? "").trim();
      if (!username || !password) {
        // Avoid leaking which field is bad
        throw new DomainError(ErrorCode.INVALID_CREDENTIALS, "Invalid credentials");
      }
      const user = await this.repo.findByUsername(username, { includeDeleted: true });
      if (!user || user.deletedAt != null) {
        throw new DomainError(ErrorCode.INVALID_CREDENTIALS, "Invalid credentials");
      }
      const ok = await argon2.verify(user.passwordHash, password);
      if (!ok) {
        throw new DomainError(ErrorCode.INVALID_CREDENTIALS, "Invalid credentials");
      }
      return toDto(user);
    } catch (err) {
      if (err instanceof DomainError) throw err;
      throw DomainError.from(err);
    }
  }

  async deactivate(userId: string): Promise<UserDTO> {
    try {
      const existing = await this.repo.findById(userId);
      if (!existing) {
        throw new DomainError(ErrorCode.NOT_FOUND, "User not found");
      }
      const updated = await this.repo.update(existing.id, { active: false, updatedAt: Date.now() });
      return toDto(updated);
    } catch (err) {
      if (err instanceof DomainError) throw err;
      throw DomainError.from(err);
    }
  }

  async activate(userId: string): Promise<UserDTO> {
    try {
      const existing = await this.repo.findById(userId);
      if (!existing) {
        throw new DomainError(ErrorCode.NOT_FOUND, "User not found");
      }
      const updated = await this.repo.update(existing.id, { active: true, updatedAt: Date.now() });
      return toDto(updated);
    } catch (err) {
      if (err instanceof DomainError) throw err;
      throw DomainError.from(err);
    }
  }

  async softDelete(userId: string): Promise<UserDTO> {
    try {
      const existing = await this.repo.findById(userId);
      if (!existing) {
        throw new DomainError(ErrorCode.NOT_FOUND, "User not found");
      }
      const now = Date.now();
      const updated = await this.repo.update(existing.id, { deletedAt: now, active: false, updatedAt: now });
      return toDto(updated);
    } catch (err) {
      if (err instanceof DomainError) throw err;
      throw DomainError.from(err);
    }
  }
}

export default AuthService;