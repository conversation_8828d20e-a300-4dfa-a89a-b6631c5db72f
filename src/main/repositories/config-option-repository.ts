import { and, desc, eq, isNull, like, sql } from 'drizzle-orm'
import { db } from '@/main/database/client'
import { configOptions } from '@/main/database/schema'
import { DomainError, ErrorCode } from '@/shared/types/errors'

export type ConfigOptionRow = {
  id: string
  category: string
  value: string
  display_name: string
  display_order: number
  active: boolean
  created_at?: string | null
  updated_at?: string | null
  deleted_at?: string | null
}

export type UpsertConfigOptionInput = {
  id?: string
  category: string
  value: string
  display_name: string
  display_order?: number
  active?: boolean
}

/**
 * ConfigOptionRepository (Drizzle/SQLite)
 * - Single-table model over config_options
 * - Enforces case-insensitive uniqueness by (category,value) among non-deleted
 * - Auto-assigns display_order if not provided
 * - Soft-deletes via deleted_at timestamp
 */
export class ConfigOptionRepository {
  constructor(private readonly client = db) {}

  // Map DB row (camel cased columns in Drizzle schema) to API snake_case row
  private mapToRow(r: typeof configOptions.$inferSelect): ConfigOptionRow {
    return {
      id: r.id,
      category: r.category,
      value: r.value,
      display_name: r.displayName,
      display_order: r.displayOrder,
      active: !!r.active,
      created_at: r.createdAt ?? null,
      updated_at: r.updatedAt ?? null,
      deleted_at: r.deletedAt ?? null,
    }
  }

  // Case-insensitive compare helper for uniqueness
  private ilike(field: any, v: string) {
    // For sqlite without ILIKE, use lower() comparison
    return eq(sql`lower(${field})`, sql`lower(${sql.placeholder('cmp')})`)
  }

  async getByCategory(category: string): Promise<ConfigOptionRow[]> {
    try {
      const list = await this.client
        .select()
        .from(configOptions)
        .where(and(eq(configOptions.category, category), isNull(configOptions.deletedAt)))
        .orderBy(configOptions.displayOrder, desc(configOptions.active))
      return list.map((r) => this.mapToRow(r))
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async upsert(input: UpsertConfigOptionInput): Promise<ConfigOptionRow> {
    const now = new Date().toISOString()
    const category = (input.category ?? '').trim()
    const value = (input.value ?? '').trim()
    const displayName = (input.display_name ?? '').trim()
    if (!category || !value || !displayName) {
      throw new DomainError(
        ErrorCode.VALIDATION_ERROR,
        'Missing required fields for config option upsert',
        {
          missing: [
            !category ? 'category' : null,
            !value ? 'value' : null,
            !displayName ? 'display_name' : null,
          ].filter(Boolean),
        }
      )
    }

    // Uniqueness: case-insensitive within category among non-deleted
    const existingSame = await this.client
      .select({ id: configOptions.id })
      .from(configOptions)
      .where(
        and(
          eq(sql`lower(${configOptions.category})`, category.toLowerCase()),
          eq(sql`lower(${configOptions.value})`, value.toLowerCase()),
          isNull(configOptions.deletedAt)
        )
      )

    // Compute display_order if not provided
    let displayOrder = input.display_order
    if (displayOrder === undefined || displayOrder === null || Number.isNaN(Number(displayOrder))) {
      const maxRow = await this.client
        .select({ max: sql<number>`coalesce(max(${configOptions.displayOrder}), 0)` })
        .from(configOptions)
        .where(and(eq(configOptions.category, category), isNull(configOptions.deletedAt)))
        .limit(1)
      const currentMax = (maxRow?.[0]?.max ?? 0) as number
      displayOrder = currentMax + 1
    } else {
      displayOrder = Number(displayOrder)
    }

    if (input.id) {
      // Update path
      // If uniqueness conflict exists with different id
      if (existingSame.length > 0) {
        const conflictId = existingSame[0].id
        if (conflictId !== input.id) {
          throw new DomainError(
            ErrorCode.CONFLICT,
            'Duplicate config option in category',
            { category, value }
          )
        }
      }
      const patch = {
        category,
        value,
        displayName,
        displayOrder,
        active: input.active ?? true,
        updatedAt: now,
      }
      const updated = await this.client
        .update(configOptions)
        .set(patch)
        .where(eq(configOptions.id, input.id))
        .returning()
      if (!updated.length) {
        throw new DomainError(ErrorCode.NOT_FOUND, 'Config option not found', { id: input.id })
      }
      return this.mapToRow(updated[0])
    } else {
      // Create path - block conflict if any active row exists
      if (existingSame.length > 0) {
        throw new DomainError(
          ErrorCode.CONFLICT,
          'Duplicate config option in category',
          { category, value }
        )
      }
      // ULID generation is typically at service layer; here use time-based random if not provided by db
      const id = crypto.randomUUID ? crypto.randomUUID() : `c_${Math.random().toString(36).slice(2)}`
      const row = await this.client
        .insert(configOptions)
        .values({
          id,
          category,
          value,
          displayName,
          displayOrder,
          active: input.active ?? true,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
        })
        .returning()
      return this.mapToRow(row[0])
    }
  }

  async softDelete(id: string): Promise<void> {
    const now = new Date().toISOString()
    try {
      const res = await this.client
        .update(configOptions)
        .set({ deletedAt: now, updatedAt: now })
        .where(eq(configOptions.id, id))
        .returning({ id: configOptions.id })
      if (!res.length) {
        throw new DomainError(ErrorCode.NOT_FOUND, 'Config option not found', { id })
      }
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }
}

export default ConfigOptionRepository