/**
 * Base Repository interface for all repositories
 * Provides common CRUD operations and soft delete functionality
 */

import { DomainError, ErrorCode } from '../../shared/types/errors';

// Generic types for repository operations
export interface FilterOptions {
  where?: Record<string, any>;
  include?: Record<string, any>;
  orderBy?: Record<string, any>;
  skip?: number;
  take?: number;
}

export interface CreateData<T> {
  [key: string]: any;
}

export interface UpdateData<T> {
  [key: string]: any;
}

/**
 * Base repository interface that all repositories must implement
 * Provides common CRUD operations with soft delete support
 */
export abstract class BaseRepository<T> {
  protected abstract tableName: string;

  /**
   * Find a single record by ID
   * @param id - The record ID to find
   * @returns The record or null if not found
   */
  abstract findById(id: string): Promise<T | null>;

  /**
   * Find multiple records with optional filters
   * @param filters - Optional filter criteria
   * @returns Array of records
   */
  abstract findMany(filters?: FilterOptions): Promise<T[]>;

  /**
   * Create a new record
   * @param data - The data to create
   * @returns The created record
   */
  abstract create(data: CreateData<T>): Promise<T>;

  /**
   * Update an existing record
   * @param id - The record ID to update
   * @param data - The data to update
   * @returns The updated record
   */
  abstract update(id: string, data: UpdateData<T>): Promise<T>;

  /**
   * Soft delete a record (set deleted_at timestamp)
   * @param id - The record ID to soft delete
   */
  abstract softDelete(id: string): Promise<void>;

  /**
   * Hard delete a record (permanent deletion)
   * @param id - The record ID to hard delete
   */
  abstract hardDelete(id: string): Promise<void>;

  /**
   * Check if a record exists by ID
   * @param id - The record ID to check
   * @returns True if record exists, false otherwise
   */
  async exists(id: string): Promise<boolean> {
    try {
      const record = await this.findById(id);
      return record !== null;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Count records matching optional filters
   * @param filters - Optional filter criteria
   * @returns The number of matching records
   */
  async count(filters?: FilterOptions): Promise<number> {
    try {
      const records = await this.findMany({
        ...filters,
        take: 1, // We only need to count, not retrieve actual data
      });
      return records.length;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Execute operations within a database transaction
   * @param operations - Array of operations to execute in transaction
   * @returns Results of all operations
   */
  async transaction(operations: Array<() => Promise<any>>): Promise<any[]> {
    // This would be implemented using the database client's transaction support
    // For now, this is a placeholder that would be overridden by specific implementations
    throw new Error('Transaction support not implemented in base repository');
  }

  /**
   * Restore a soft-deleted record
   * @param id - The record ID to restore
   * @returns The restored record
   */
  async restore(id: string): Promise<T> {
    try {
      // Remove the deleted_at timestamp to restore the record
      return await this.update(id, { deleted_at: null });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Find records by IDs
   * @param ids - Array of record IDs to find
   * @returns Array of found records
   */
  async findByIds(ids: string[]): Promise<T[]> {
    try {
      if (ids.length === 0) {
        return [];
      }

      return await this.findMany({
        where: { id: { in: ids } },
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Find the first record matching the given conditions
   * @param filters - Filter criteria
   * @returns The first matching record or null
   */
  async findFirst(filters?: FilterOptions): Promise<T | null> {
    try {
      const results = await this.findMany({
        ...filters,
        take: 1,
      });
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Check if any records match the given conditions
   * @param filters - Filter criteria
   * @returns True if any records match, false otherwise
   */
  async any(filters?: FilterOptions): Promise<boolean> {
    try {
      const record = await this.findFirst(filters);
      return record !== null;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Get soft-deleted records
   * @param filters - Optional filter criteria
   * @returns Array of soft-deleted records
   */
  async findDeleted(filters?: FilterOptions): Promise<T[]> {
    try {
      return await this.findMany({
        ...filters,
        where: {
          ...filters?.where,
          deleted_at: { not: null },
        },
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  /**
   * Get all records (including soft-deleted)
   * @param filters - Optional filter criteria
   * @returns Array of all records
   */
  async findAll(filters?: FilterOptions): Promise<T[]> {
    try {
      return await this.findMany({
        ...filters,
        where: filters?.where, // Don't automatically filter out deleted records
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }
}