import { DomainError, ErrorCode } from '@/shared/types/errors';
import { generateId as ulid } from '@/main/utils/ulid';

type RawVoter = any;

export interface VoterSearchFilters {
  polling_station_id?: string;
  section_id?: string | 'unassigned';
  status?: string | string[];
  supporter_status?: string;
  education?: string;
  occupation?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  limit?: number;
  offset?: number;
  take?: number;
  skip?: number;
  orderBy?: { name?: 'asc' | 'desc' };
}

export class VoterRepository {
  private db: any;
  private generateId: () => string;

  constructor(dbClient: any, generateId: () => string = ulid) {
    this.db = dbClient;
    this.generateId = generateId;
  }

  private now() {
    return new Date().toISOString();
  }

  // Validation moved to service layer - repository focuses on data access only

  private normalizeInput(data: any) {
    // Accept both snake_case and camelCase, output snake_case keys for dbClient
    const out: any = {};
    const map: Record<string, string> = {
      id: 'id',
      name: 'name',
      relationship_type: 'relationship_type',
      relationshipType: 'relationship_type',
      relationship_name: 'relationship_name',
      relationshipName: 'relationship_name',
      gender: 'gender',
      birth_year: 'birth_year',
      birthYear: 'birth_year',
      epic_number: 'epic_number',
      epicNumber: 'epic_number',
      house_number: 'house_number',
      houseNumber: 'house_number',
      polling_station_id: 'polling_station_id',
      pollingStationId: 'polling_station_id',
      section_id: 'section_id',
      sectionId: 'section_id',
      phone: 'phone',
      email: 'email',
      facebook: 'facebook',
      instagram: 'instagram',
      twitter: 'twitter',
      status: 'status',
      supporter_status: 'supporter_status',
      supporterStatus: 'supporter_status',
      education: 'education',
      occupation: 'occupation',
      community: 'community',
      religion: 'religion',
      economic_status: 'economic_status',
      economicStatus: 'economic_status',
      custom_notes: 'custom_notes',
      customNotes: 'custom_notes',
      created_at: 'created_at',
      updated_at: 'updated_at',
      deleted_at: 'deleted_at',
    };
    for (const k of Object.keys(data || {})) {
      const t = map[k] || k;
      out[t] = data[k];
    }
    return out;
  }

  async findByEpicNumber(epicNumber: string): Promise<RawVoter | null> {
    try {
      const result = await this.db.voters.findFirst({
        where: { epic_number: epicNumber, deleted_at: null },
      });
      return result ?? null;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async search(query: string, filters?: any): Promise<RawVoter[]> {
    try {
      // If only query is provided, delegate to FTS mock per tests
      const hasFilters = filters && Object.keys(filters).length > 0;
      if (!hasFilters && this.db.voters_fts?.search) {
        return await this.db.voters_fts.search(query);
      }

      // Combine filters with deleted_at: null
      const where: any = { deleted_at: null };
      const f = this.normalizeInput(filters || {});
      if (f.polling_station_id) where.polling_station_id = f.polling_station_id;
      if (f.section_id !== undefined) where.section_id = f.section_id === 'unassigned' ? null : f.section_id;
      if (f.status) where.status = f.status;

      return await this.db.voters.findMany({ where });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  // Efficient consolidated method for all filtering operations
  async findByFilters(filters: VoterSearchFilters): Promise<RawVoter[]> {
    try {
      const where: any = { deleted_at: null };

      if (filters.polling_station_id) where.polling_station_id = filters.polling_station_id;
      if (filters.section_id !== undefined) {
        where.section_id = filters.section_id === 'unassigned' ? null : filters.section_id;
      }
      if (filters.status) {
        where.status = Array.isArray(filters.status) ? { in: filters.status } : filters.status;
      }

      const query: any = { where };
      if (filters.skip !== undefined) query.skip = filters.skip;
      if (filters.take !== undefined) query.take = filters.take;
      if (filters.orderBy) query.orderBy = filters.orderBy;

      return await this.db.voters.findMany(query);
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  // Efficient backward compatibility methods - delegate to findByFilters
  async findByPollingStation(pollingStationId: string, filters?: VoterSearchFilters): Promise<RawVoter[]> {
    return this.findByFilters({
      ...filters,
      polling_station_id: pollingStationId
    });
  }

  async findBySection(sectionId: string): Promise<RawVoter[]> {
    return this.findByFilters({ section_id: sectionId });
  }

  async findByStatus(status: string | string[]): Promise<RawVoter[]> {
    return this.findByFilters({ status });
  }

  // Backward compatibility methods - use findByFilters internally
  async findByPollingStation(pollingStationId: string, filters?: VoterSearchFilters): Promise<RawVoter[]> {
    return this.findByFilters({
      ...filters,
      polling_station_id: pollingStationId
    });
  }

  async findBySection(sectionId: string): Promise<RawVoter[]> {
    return this.findByFilters({ section_id: sectionId });
  }

  async findByStatus(status: string | string[]): Promise<RawVoter[]> {
    return this.findByFilters({ status });
  }

  async getStatistics(): Promise<any> {
    try {
      // Get all voters once and calculate statistics in memory
      const allVoters = await this.db.voters.findMany({ where: { deleted_at: null } });

      const stats = {
        total: allVoters.length,
        active: 0,
        expired: 0,
        shifted: 0,
        missing: 0,
        duplicate: 0,
        disqualified: 0,
      };

      allVoters.forEach(voter => {
        switch (voter.status) {
          case 'Active': stats.active++; break;
          case 'Expired': stats.expired++; break;
          case 'Shifted': stats.shifted++; break;
          case 'Missing': stats.missing++; break;
          case 'Duplicate': stats.duplicate++; break;
          case 'Disqualified': stats.disqualified++; break;
        }
      });

      return stats;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async count(filters?: VoterSearchFilters): Promise<number> {
    try {
      const where: any = { deleted_at: null };
      if (filters?.polling_station_id) where.polling_station_id = filters.polling_station_id;
      if (filters?.section_id !== undefined) {
        where.section_id = filters.section_id === 'unassigned' ? null : filters.section_id;
      }
      if (filters?.status) where.status = filters.status;
      const arr = await this.db.voters.findMany({ where });
      return arr.length;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async findById(id: string): Promise<RawVoter | null> {
    try {
      const result = await this.db.voters.findFirst({
        where: { id, deleted_at: null },
      });
      return result ?? null;
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  // Alias for backward compatibility - use findByFilters instead
  async findMany(filters?: any): Promise<RawVoter[]> {
    const normalizedFilters = this.normalizeInput(filters?.where ? { ...filters.where } : filters || {});
    return this.findByFilters({
      ...normalizedFilters,
      skip: filters?.skip,
      take: filters?.take,
      orderBy: filters?.orderBy,
    });
  }

  async create(data: any): Promise<RawVoter> {
    try {
      const normalized = this.normalizeInput(data);

      // Check for duplicate EPIC among active records
      if (normalized.epic_number) {
        const existing = await this.db.voters.findFirst({
          where: { epic_number: normalized.epic_number, deleted_at: null },
        });
        if (existing) {
          throw new DomainError(
            ErrorCode.CONFLICT,
            'Duplicate EPIC number',
            { epic_number: normalized.epic_number }
          );
        }
      }

      const now = this.now();
      const payload = {
        ...normalized,
        id: normalized.id || this.generateId(),
        created_at: now,
        updated_at: now,
      };

      return await this.db.voters.create({ data: payload });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async update(id: string, data: any): Promise<RawVoter> {
    try {
      const normalized = this.normalizeInput(data);

      // Check for duplicate EPIC if being updated
      if (normalized.epic_number !== undefined) {
        const existing = await this.db.voters.findFirst({
          where: { epic_number: normalized.epic_number, deleted_at: null },
        });
        if (existing && existing.id !== id) {
          throw new DomainError(
            ErrorCode.CONFLICT,
            'Duplicate EPIC number',
            { epic_number: normalized.epic_number }
          );
        }
      }

      const payload = {
        ...normalized,
        updated_at: this.now(),
      };

      return await this.db.voters.update({
        where: { id },
        data: payload,
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async softDelete(id: string): Promise<void> {
    try {
      await this.db.voters.update({
        where: { id },
        data: { deleted_at: this.now() },
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }

  async hardDelete(id: string): Promise<void> {
    try {
      await this.db.voters.delete({
        where: { id },
      });
    } catch (error) {
      throw DomainError.from(error, ErrorCode.TRANSIENT_FAILURE);
    }
  }
}