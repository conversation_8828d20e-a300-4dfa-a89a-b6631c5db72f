import { eq, and, isNull, sql } from 'drizzle-orm'
import { drizzle } from 'drizzle-orm/better-sqlite3'
import Database from 'better-sqlite3'
import { voters, voterTurnout } from '@/main/database/schema'
import { DomainError, ErrorCode } from '@/shared/types/errors'
import { TurnoutRecord, DemographicDimension } from '@/main/services/turnout-service'

type DrizzleDb = ReturnType<typeof drizzle>

function mapRowToRecord(row: any): TurnoutRecord {
  return {
    id: row.id,
    voterId: row.voter_id ?? row.voterId,
    electionYear: row.election_year ?? row.electionYear,
    voted: !!row.voted,
    // voters_turnout schema does not have notes column; keep DTO compatibility with undefined/null
    notes: null,
    createdAt: row.created_at ?? row.createdAt,
    updatedAt: row.updated_at ?? row.updatedAt,
    deletedAt: (row.deleted_at ?? row.deletedAt) ?? null,
  }
}

export class TurnoutRepositorySqlite {
  constructor(private readonly db: DrizzleDb) {}

  async findById(id: string): Promise<TurnoutRecord | null> {
    try {
      const rows = await this.db
        .select({
          id: voterTurnout.id,
          voter_id: voterTurnout.voterId,
          election_year: voterTurnout.electionYear,
          voted: voterTurnout.voted,
          created_at: voterTurnout.createdAt,
          updated_at: voterTurnout.updatedAt,
          deleted_at: voterTurnout.deletedAt,
        })
        .from(voterTurnout)
        .where(and(eq(voterTurnout.id, id), isNull(voterTurnout.deletedAt)))
        .limit(1)

      return rows.length ? mapRowToRecord(rows[0]) : null
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async find(voterId: string, electionYear: number): Promise<TurnoutRecord | null> {
    try {
      const rows = await this.db
        .select({
          id: voterTurnout.id,
          voter_id: voterTurnout.voterId,
          election_year: voterTurnout.electionYear,
          voted: voterTurnout.voted,
          created_at: voterTurnout.createdAt,
          updated_at: voterTurnout.updatedAt,
          deleted_at: voterTurnout.deletedAt,
        })
        .from(voterTurnout)
        .where(
          and(
            eq(voterTurnout.voterId, voterId),
            eq(voterTurnout.electionYear, electionYear),
            isNull(voterTurnout.deletedAt)
          )
        )
        .limit(1)

      return rows.length ? mapRowToRecord(rows[0]) : null
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async upsert(input: { voterId: string; electionYear: number; voted: boolean; notes?: string | null }): Promise<TurnoutRecord> {
    try {
      const now = new Date().toISOString()

      // Check existing active record (soft-delete aware uniqueness)
      const existing = await this.find(input.voterId, input.electionYear)
      if (existing) {
        const rows = await this.db
          .update(voterTurnout)
          .set({
            voted: input.voted,
            // no notes column in schema; ignore
            updatedAt: now,
          })
          .where(eq(voterTurnout.id, existing.id))
          .returning({
            id: voterTurnout.id,
            voter_id: voterTurnout.voterId,
            election_year: voterTurnout.electionYear,
            voted: voterTurnout.voted,
            created_at: voterTurnout.createdAt,
            updated_at: voterTurnout.updatedAt,
            deleted_at: voterTurnout.deletedAt,
          })
        return mapRowToRecord(rows[0])
      }

      // Insert new
      const id = crypto.randomUUID()
      const rows = await this.db
        .insert(voterTurnout)
        .values({
          id,
          voterId: input.voterId,
          electionYear: input.electionYear,
          voted: input.voted,
          createdAt: now,
          updatedAt: now,
          deletedAt: null,
        })
        .returning({
          id: voterTurnout.id,
          voter_id: voterTurnout.voterId,
          election_year: voterTurnout.electionYear,
          voted: voterTurnout.voted,
          created_at: voterTurnout.createdAt,
          updated_at: voterTurnout.updatedAt,
          deleted_at: voterTurnout.deletedAt,
        })

      return mapRowToRecord(rows[0])
    } catch (err: any) {
      // Map SQLite unique violation to DomainError.CONFLICT
      const msg = String(err?.message || '')
      if (msg.includes('idx_turnout_voter_year') || msg.includes('UNIQUE')) {
        throw new DomainError(ErrorCode.CONFLICT, 'Turnout already recorded for voter and year')
      }
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async delete(id: string): Promise<void> {
    try {
      const now = new Date().toISOString()
      await this.db
        .update(voterTurnout)
        .set({ deletedAt: now, updatedAt: now })
        .where(eq(voterTurnout.id, id))
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async listByVoter(voterId: string): Promise<TurnoutRecord[]> {
    try {
      const rows = await this.db
        .select({
          id: voterTurnout.id,
          voter_id: voterTurnout.voterId,
          election_year: voterTurnout.electionYear,
          voted: voterTurnout.voted,
          created_at: voterTurnout.createdAt,
          updated_at: voterTurnout.updatedAt,
          deleted_at: voterTurnout.deletedAt,
        })
        .from(voterTurnout)
        .where(and(eq(voterTurnout.voterId, voterId), isNull(voterTurnout.deletedAt)))
        .orderBy(voterTurnout.electionYear)

      return rows.map(mapRowToRecord)
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async aggregateByStation(electionYear: number): Promise<Array<{ pollingStationId: string; total: number; voted: number }>> {
    try {
      const rows = await this.db
        .select({
          pollingStationId: voters.pollingStationId,
          total: sql<number>`COUNT(*)`.as('total'),
          voted: sql<number>`SUM(CASE WHEN ${voterTurnout.voted} = 1 THEN 1 ELSE 0 END)`.as('voted'),
        })
        .from(voterTurnout)
        .leftJoin(voters, and(eq(voters.id, voterTurnout.voterId), isNull(voters.deletedAt)))
        .where(and(eq(voterTurnout.electionYear, electionYear), isNull(voterTurnout.deletedAt)))
        .groupBy(voters.pollingStationId)

      return rows.map((r: any) => ({
        pollingStationId: r.pollingStationId,
        total: Number(r.total || 0),
        voted: Number(r.voted || 0),
      }))
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async aggregateBySection(
    electionYear: number,
    pollingStationId?: string
  ): Promise<Array<{ sectionId: string | null; total: number; voted: number }>> {
    try {
      const whereParts = [eq(voterTurnout.electionYear, electionYear), isNull(voterTurnout.deletedAt)]
      if (pollingStationId) {
        whereParts.push(eq(voters.pollingStationId, pollingStationId))
      }

      const rows = await this.db
        .select({
          sectionId: voters.sectionId,
          total: sql<number>`COUNT(*)`.as('total'),
          voted: sql<number>`SUM(CASE WHEN ${voterTurnout.voted} = 1 THEN 1 ELSE 0 END)`.as('voted'),
        })
        .from(voterTurnout)
        .leftJoin(voters, and(eq(voters.id, voterTurnout.voterId), isNull(voters.deletedAt)))
        .where(and(...whereParts))
        .groupBy(voters.sectionId)

      return rows.map((r: any) => ({
        sectionId: r.sectionId ?? null,
        total: Number(r.total || 0),
        voted: Number(r.voted || 0),
      }))
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }

  async aggregateByDemographic(
    electionYear: number,
    dimension: DemographicDimension
  ): Promise<Array<{ key: string; total: number; voted: number }>> {
    try {
      // Map dimension to voters column
      const dimColumn = (() => {
        switch (dimension) {
          case 'gender':
            return voters.gender
          case 'education':
            return voters.education
          case 'occupation':
            return voters.occupation
          case 'community':
            return voters.community
          case 'religion':
            return voters.religion
          case 'economic_status':
            return voters.economicStatus
          case 'supporter_status':
            return voters.supporterStatus
          default:
            return voters.gender
        }
      })()

      const rows = await this.db
        .select({
          key: dimColumn,
          total: sql<number>`COUNT(*)`.as('total'),
          voted: sql<number>`SUM(CASE WHEN ${voterTurnout.voted} = 1 THEN 1 ELSE 0 END)`.as('voted'),
        })
        .from(voterTurnout)
        .leftJoin(voters, and(eq(voters.id, voterTurnout.voterId), isNull(voters.deletedAt)))
        .where(and(eq(voterTurnout.electionYear, electionYear), isNull(voterTurnout.deletedAt)))
        .groupBy(dimColumn)

      return rows.map((r: any) => ({
        key: String(r.key ?? 'Unknown'),
        total: Number(r.total || 0),
        voted: Number(r.voted || 0),
      }))
    } catch (err) {
      throw DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
    }
  }
}

export default TurnoutRepositorySqlite