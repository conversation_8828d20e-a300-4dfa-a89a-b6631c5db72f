import { sqlite } from '../database/client'
import { generateId } from '../utils/ulid'

export type ExportJobRow = {
  id: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'canceled'
  progress: number
  message: string | null
  result_path: string | null
  error_report_path: string | null
  options: any | null
  filters: any | null
  checkpoint: any | null
  created_at: string
  updated_at: string
  completed_at: string | null
}

export type CreateExportJobInput = {
  type: 'csv' | 'pdf'
  filters?: Record<string, unknown>
  anonymize?: boolean
}

export default class ExportJobRepository {
  async create(input: CreateExportJobInput): Promise<{ id: string }> {
    const id = generateId()
    const now = new Date().toISOString()
    const row: ExportJobRow = {
      id,
      status: 'pending',
      progress: 0,
      message: null,
      result_path: null,
      error_report_path: null,
      options: { type: input.type, anonymize: !!input.anonymize },
      filters: input.filters ?? {},
      checkpoint: null,
      created_at: now,
      updated_at: now,
      completed_at: null,
    }

    const sql = `INSERT INTO export_jobs
      (id,status,progress,message,result_path,error_report_path,options,filters,checkpoint,created_at,updated_at,completed_at)
      VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`
    const stmt = sqlite.prepare(sql)
    stmt.run(
      row.id,
      row.status,
      Number(row.progress),
      row.message,
      row.result_path,
      row.error_report_path,
      row.options ? JSON.stringify(row.options) : null,
      row.filters ? JSON.stringify(row.filters) : null,
      row.checkpoint ? JSON.stringify(row.checkpoint) : null,
      row.created_at,
      row.updated_at,
      row.completed_at,
    )
    return { id }
  }

  async getById(id: string): Promise<ExportJobRow | null> {
    const sql = `SELECT id,status,progress,message,result_path,error_report_path,options,filters,checkpoint,created_at,updated_at,completed_at
                 FROM export_jobs WHERE id = ? LIMIT 1`
    const row: any = sqlite.prepare(sql).get(id)
    if (!row) return null
    return {
      id: row.id as string,
      status: row.status as ExportJobRow['status'],
      progress: Number(row.progress ?? 0),
      message: row.message ?? null,
      result_path: row.result_path ?? null,
      error_report_path: row.error_report_path ?? null,
      options: parseJson(row.options),
      filters: parseJson(row.filters),
      checkpoint: parseJson(row.checkpoint),
      created_at: row.created_at,
      updated_at: row.updated_at,
      completed_at: row.completed_at ?? null,
    }
  }

  async update(
    id: string,
    patch: Partial<{
      status: ExportJobRow['status']
      progress: number
      message: string | null
      result_path: string | null
      error_report_path: string | null
      options: any | null
      filters: any | null
      checkpoint: any | null
      completed_at: string | null
    }>,
  ): Promise<void> {
    const existing = await this.getById(id)
    if (!existing) throw new Error('NOT_FOUND: job')

    const merged: ExportJobRow = {
      ...existing,
      ...patch,
      options: patch.options !== undefined ? patch.options : existing.options,
      filters: patch.filters !== undefined ? patch.filters : existing.filters,
      checkpoint: patch.checkpoint !== undefined ? patch.checkpoint : existing.checkpoint,
      progress: clamp(typeof patch.progress === 'number' ? patch.progress : existing.progress, 0, 100),
      updated_at: new Date().toISOString(),
    }

    const sql = `UPDATE export_jobs
      SET status = ?, progress = ?, message = ?, result_path = ?, error_report_path = ?,
          options = ?, filters = ?, checkpoint = ?, updated_at = ?, completed_at = ?
      WHERE id = ?`
    sqlite.prepare(sql).run(
      merged.status,
      Number(merged.progress),
      merged.message,
      merged.result_path,
      merged.error_report_path,
      merged.options ? JSON.stringify(merged.options) : null,
      merged.filters ? JSON.stringify(merged.filters) : null,
      merged.checkpoint ? JSON.stringify(merged.checkpoint) : null,
      merged.updated_at,
      merged.completed_at,
      id,
    )
  }

  async cancel(id: string): Promise<void> {
    const job = await this.getById(id)
    if (!job) throw new Error('NOT_FOUND: job')
    const now = new Date().toISOString()
    const sql = `UPDATE export_jobs
                 SET status = 'canceled', updated_at = ?, completed_at = COALESCE(completed_at, ?)
                 WHERE id = ?`
    sqlite.prepare(sql).run(now, now, id)
  }
}

function parseJson(value: any) {
  if (value == null) return null
  if (typeof value === 'object') return value
  try {
    return JSON.parse(String(value))
  } catch {
    return null
  }
}

function clamp(n: number, min: number, max: number) {
  return Math.max(min, Math.min(max, n))
}