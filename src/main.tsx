import React from 'react'
import <PERSON><PERSON><PERSON><PERSON> from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Only access window.electronAPI if it exists (avoid runtime crashes in dev)
const hasElectronApi = typeof window !== 'undefined' && Object.prototype.hasOwnProperty.call(window, 'electronAPI');
if (!hasElectronApi) {
  // eslint-disable-next-line no-console
  console.warn('window.electronAPI is undefined. Ensure preload is loaded and contextIsolation is enabled.')
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// Use contextBridge
window.ipcRenderer.on('main-process-message', (_event, message) => {
  console.log(message)
})
