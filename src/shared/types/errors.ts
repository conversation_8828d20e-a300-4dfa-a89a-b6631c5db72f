/**
 * Domain error types for the voter management system
 */

export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CONFLICT = 'CONFLICT',
  NOT_FOUND = 'NOT_FOUND',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  TRANSIENT_FAILURE = 'TRANSIENT_FAILURE',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
}

export class DomainError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: any,
    public userMessage?: string
  ) {
    super(message);
    this.name = 'DomainError';

    // Ensure proper prototype chain
    if (Object.setPrototypeOf) {
      Object.setPrototypeOf(this, DomainError.prototype);
    } else {
      (this as any).__proto__ = DomainError.prototype;
    }
  }

  /**
   * Create a DomainError from any error type
   */
  static from(error: any, defaultCode: ErrorCode = ErrorCode.TRANSIENT_FAILURE): DomainError {
    if (error instanceof DomainError) {
      return error;
    }

    let code = defaultCode;
    let message = 'An unexpected error occurred';
    // Tests expect exact preservation of originalError without extra fields like stack
    let details: any = { originalError: (error && (error.name || error.constructor?.name)) || 'Error' };

    if (error instanceof Error) {
      message = error.message || message;
      // keep details as only originalError per test expectation
      details = { originalError: error.name || 'Error' };
    }

    if (typeof error === 'object' && error !== null) {
      if ((error as any).code === 'SQLITE_CONSTRAINT') {
        code = ErrorCode.CONFLICT;
        message = 'Database constraint violation';
      } else if ((error as any).code === 'SQLITE_NOTFOUND') {
        code = ErrorCode.NOT_FOUND;
        message = 'Resource not found';
      }
    }

    return new DomainError(code, message, details);
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    return this.userMessage || this.getDefaultUserMessage();
  }

  /**
   * Get default user-friendly message based on error code
   */
  private getDefaultUserMessage(): string {
    switch (this.code) {
      case ErrorCode.VALIDATION_ERROR:
        return 'Invalid input data. Please check your entries.';
      case ErrorCode.CONFLICT:
        return 'This record already exists or conflicts with existing data.';
      case ErrorCode.NOT_FOUND:
        return 'The requested resource was not found.';
      case ErrorCode.PERMISSION_DENIED:
        return 'You do not have permission to perform this action.';
      case ErrorCode.TRANSIENT_FAILURE:
        return 'A temporary error occurred. Please try again.';
      default:
        return 'An unexpected error occurred.';
    }
  }
}

/**
 * Error response structure for API responses
 */
export interface ErrorResponse {
  code: ErrorCode;
  message: string;
  details?: any;
  userMessage?: string;
  timestamp: string;
}

/**
 * Convert DomainError to ErrorResponse
 */
export function domainErrorToResponse(error: DomainError): ErrorResponse {
  return {
    code: error.code,
    message: error.message,
    details: error.details,
    userMessage: error.getUserMessage(),
    timestamp: new Date().toISOString(),
  };
}