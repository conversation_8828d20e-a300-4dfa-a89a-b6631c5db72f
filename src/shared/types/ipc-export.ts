import { z } from 'zod'

// Export channel request/response schemas

export const ExportTypeSchema = z.enum(['csv', 'pdf'])

const AnyRecord = z.record(z.string(), z.unknown())

export const ExportStartRequestSchema = z.object({
  type: ExportTypeSchema,
  anonymize: z.boolean().optional().default(false),
  // Supported initial filters: pollingStationId, sectionId, status
  filters: z
    .object({
      pollingStationId: z.string().min(1).optional(),
      sectionId: z.string().min(1).optional(),
      status: z.enum(['Active', 'Expired', 'Shifted', 'Missing', 'Duplicate', 'Disqualified']).optional(),
    })
    .partial()
    .optional()
    .nullable(),
})

export type ExportStartRequest = z.infer<typeof ExportStartRequestSchema>

export const ExportStartResponseSchema = z.object({
  jobId: z.string().min(1),
})
export type ExportStartResponse = z.infer<typeof ExportStartResponseSchema>

export const ExportStatusRequestSchema = z.object({
  id: z.string().min(1),
})
export type ExportStatusRequest = z.infer<typeof ExportStatusRequestSchema>

export const ExportStatusResponseSchema = z.object({
  id: z.string(),
  status: z.enum(['pending', 'running', 'completed', 'failed', 'canceled']),
  progress: z.number().min(0).max(100),
  message: z.string().nullable().optional(),
  resultPath: z.string().nullable().optional(),
  errorReportPath: z.string().nullable().optional(),
  options: AnyRecord.nullable().optional(),
  filters: z
    .object({
      pollingStationId: z.string().min(1).optional(),
      sectionId: z.string().min(1).optional(),
      status: z.enum(['Active', 'Expired', 'Shifted', 'Missing', 'Duplicate', 'Disqualified']).optional(),
    })
    .partial()
    .nullable()
    .optional(),
  checkpoint: AnyRecord.nullable().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  completedAt: z.string().nullable().optional(),
})
export type ExportStatusResponse = z.infer<typeof ExportStatusResponseSchema>

export const ExportCancelRequestSchema = z.object({
  id: z.string().min(1),
})
export type ExportCancelRequest = z.infer<typeof ExportCancelRequestSchema>