/**
 * Database entity types that match the schema
 */

import { z } from 'zod';

// Base entity type with common fields
export const BaseEntitySchema = z.object({
  id: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  deletedAt: z.string().optional(),
});

export type BaseEntity = z.infer<typeof BaseEntitySchema>;

// Voter type matching database schema
export const VoterSchema = BaseEntitySchema.extend({
  name: z.string(),
  relationshipType: z.enum(['Father', 'Mother', 'Husband', 'Others']),
  relationshipName: z.string(),
  gender: z.enum(['Male', 'Female', 'Other']),
  birthYear: z.number().optional(),
  epicNumber: z.string(),
  houseNumber: z.string(),
  pollingStationId: z.string(),
  sectionId: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().optional(),
  facebook: z.string().optional(),
  instagram: z.string().optional(),
  twitter: z.string().optional(),
  status: z.enum(['Active', 'Expired', 'Shifted', 'Missing', 'Duplicate', 'Disqualified']),
  supporterStatus: z.string().optional(),
  education: z.string().optional(),
  occupation: z.string().optional(),
  community: z.string().optional(),
  religion: z.string().optional(),
  economicStatus: z.string().optional(),
  customNotes: z.string().optional(),
});

export type Voter = z.infer<typeof VoterSchema>;

// Polling Station type
export const PollingStationSchema = BaseEntitySchema.extend({
  name: z.string(),
  code: z.string(),
});

export type PollingStation = z.infer<typeof PollingStationSchema>;

// Section type
export const SectionSchema = BaseEntitySchema.extend({
  pollingStationId: z.string(),
  name: z.string(),
  code: z.string(),
});

export type Section = z.infer<typeof SectionSchema>;

// User type
export const UserSchema = BaseEntitySchema.extend({
  username: z.string(),
  passwordHash: z.string(),
  role: z.enum(['owner', 'admin', 'editor', 'viewer']),
  fullName: z.string().optional(),
  active: z.boolean(),
  lastLogin: z.string().optional(),
  sessionExpires: z.string().optional(),
});

export type User = z.infer<typeof UserSchema>;

// Transaction type
export const TransactionSchema = BaseEntitySchema.extend({
  voterId: z.string(),
  transactionDate: z.string(),
  purpose: z.string(),
  amount: z.number(),
});

export type Transaction = z.infer<typeof TransactionSchema>;

// Voter Turnout type
export const VoterTurnoutSchema = BaseEntitySchema.extend({
  voterId: z.string(),
  electionYear: z.number(),
  voted: z.boolean(),
});

export type VoterTurnout = z.infer<typeof VoterTurnoutSchema>;

// Config Option type
export const ConfigOptionSchema = BaseEntitySchema.extend({
  category: z.string(),
  value: z.string(),
  displayName: z.string(),
  displayOrder: z.number(),
  active: z.boolean(),
});

export type ConfigOption = z.infer<typeof ConfigOptionSchema>;