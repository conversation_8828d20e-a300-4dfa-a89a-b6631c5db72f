/**
 * Zod schemas for form validation and IPC payloads
 */

import { z } from 'zod';

// =====================
// Voter Schemas
// =====================

export const VoterInputSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  relationship_type: z.enum(['Father', 'Mother', 'Husband', 'Others'], 'Invalid relationship type'),
  relationship_name: z.string().min(1, 'Relationship name is required').max(255),
  gender: z.enum(['Male', 'Female', 'Other'], 'Invalid gender'),
  birth_year: z.number().min(1900).max(new Date().getFullYear() - 18).optional(),
  epic_number: z
    .string()
    .regex(/^[A-Z]{3}\d{7}$/, 'EPIC must be 3 uppercase letters followed by 7 digits'),
  house_number: z.string().min(1, 'House number is required').max(100),
  polling_station_id: z.string().min(1, 'Polling station is required'),
  section_id: z.string().optional(),
  phone: z.string().max(20).optional(),
  email: z.string().email().optional().or(z.literal('')),
  facebook: z.string().url().optional().or(z.literal('')),
  instagram: z.string().url().optional().or(z.literal('')),
  twitter: z.string().url().optional().or(z.literal('')),
  status: z.enum(['Active', 'Expired', 'Shifted', 'Missing', 'Duplicate', 'Disqualified'], 'Invalid status'),
  supporter_status: z.string().optional(),
  education: z.string().optional(),
  occupation: z.string().optional(),
  community: z.string().optional(),
  religion: z.string().optional(),
  economic_status: z.string().optional(),
  custom_notes: z.string().optional(),
});

export const VoterUpdateSchema = VoterInputSchema.partial().extend({
  id: z.string().min(1, 'Voter ID is required'),
});

export const VoterSearchFiltersSchema = z.object({
  polling_station_id: z.string().optional(),
  section_id: z.string().optional(),
  status: z.enum(['Active', 'Expired', 'Shifted', 'Missing', 'Duplicate', 'Disqualified']).optional(),
  supporter_status: z.string().optional(),
  education: z.string().optional(),
  occupation: z.string().optional(),
  community: z.string().optional(),
  religion: z.string().optional(),
  economic_status: z.string().optional(),
  limit: z.number().min(1).max(1000).default(100),
  offset: z.number().min(0).default(0),
});

// =====================
// User Schemas
// =====================

export const UserInputSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters').max(50),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  role: z.enum(['owner', 'admin', 'editor', 'viewer'], 'Invalid role'),
  full_name: z.string().max(255).optional(),
  active: z.boolean().default(true),
});

export const UserUpdateSchema = UserInputSchema.partial().extend({
  id: z.string().min(1, 'User ID is required'),
});

export const UserLoginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

export const UserChangePasswordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z
    .string()
    .min(8, 'New password must be at least 8 characters')
    .max(100, 'New password too long'),
});

// =====================
// Config Option Schemas
// =====================

export const ConfigOptionInputSchema = z.object({
  category: z.string().min(1, 'Category is required').max(50),
  value: z.string().min(1, 'Value is required').max(100),
  display_name: z.string().min(1, 'Display name is required').max(255),
  display_order: z.number().min(0),
  active: z.boolean().default(true),
});

export const ConfigOptionUpdateSchema = ConfigOptionInputSchema.partial().extend({
  id: z.string().min(1, 'Config option ID is required'),
});

// =====================
// Transaction Schemas
// =====================

export const TransactionInputSchema = z.object({
  voter_id: z.string().min(1, 'Voter is required'),
  transaction_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  purpose: z.string().min(1, 'Purpose is required').max(100),
  amount: z.number().int('Amount must be a whole number').min(0, 'Amount cannot be negative'),
});

export const TransactionUpdateSchema = TransactionInputSchema.partial().extend({
  id: z.string().min(1, 'Transaction ID is required'),
});

// =====================
// Voter Turnout Schemas
// =====================

export const VoterTurnoutInputSchema = z.object({
  voter_id: z.string().min(1, 'Voter is required'),
  election_year: z.number().int('Year must be a whole number').min(2000).max(new Date().getFullYear() + 1),
  voted: z.boolean(),
  notes: z.string().optional(),
});

export const VoterTurnoutUpdateSchema = VoterTurnoutInputSchema.partial().extend({
  id: z.string().min(1, 'Turnout record ID is required'),
});

// =====================
// Export Schemas
// =====================

export const ExportRequestSchema = z.object({
  format: z.enum(['csv', 'pdf'], 'Invalid export format'),
  type: z.enum(['voters', 'transactions', 'turnout', 'statistics'], 'Invalid export type'),
  filters: z.record(z.string(), z.any()).optional(),
  fields: z.array(z.string()).optional(),
  include_anonymized: z.boolean().default(false),
});

// =====================
// Polling Station Schemas (moved from inline definitions for uniformity)
// =====================
export const PollingStationInputSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  address: z.string().optional(),
  code: z.string().optional(),
});
export const PollingStationUpdateSchema = PollingStationInputSchema.partial().extend({
  id: z.string().min(1, 'Polling station ID is required'),
});
export type PollingStationInput = z.infer<typeof PollingStationInputSchema>;
export type PollingStationUpdate = z.infer<typeof PollingStationUpdateSchema>;

// =====================
// Section Schemas
// =====================
export const SectionInputSchema = z.object({
  pollingStationId: z.string().min(1, 'Polling station is required'),
  name: z.string().min(1, 'Name is required'),
  code: z.string().optional(),
});
export const SectionUpdateSchema = SectionInputSchema.partial().extend({
  id: z.string().min(1, 'Section ID is required'),
});
export type SectionInput = z.infer<typeof SectionInputSchema>;
export type SectionUpdate = z.infer<typeof SectionUpdateSchema>;

// =====================
// Import Schemas
// =====================

export const ImportRequestSchema = z.object({
  file_path: z.string().min(1, 'File path is required'),
  mapping: z.record(z.string(), z.string()).optional(),
  skip_validation: z.boolean().default(false),
});

export const ImportStatusSchema = z.object({
  job_id: z.string(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']),
  progress: z.number().min(0).max(100),
  total_records: z.number().min(0),
  processed_records: z.number().min(0),
  successful_records: z.number().min(0),
  error_records: z.number().min(0),
  errors: z.array(z.object({
    row: z.number(),
    column: z.string(),
    message: z.string(),
  })).optional(),
  created_at: z.string(),
  updated_at: z.string(),
});

// =====================
// Search Schemas
// =====================

export const SearchRequestSchema = z.object({
  query: z.string().min(1, 'Search query is required').max(255),
  filters: z.record(z.string(), z.any()).optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

// =====================
// Response Schemas
// =====================

export const PaginatedResponseSchema = z.object({
  data: z.array(z.any()),
  pagination: z.object({
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
  }),
});

export const SuccessResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.any().optional(),
});

// =====================
// Type Exports
// =====================

export type VoterInput = z.infer<typeof VoterInputSchema>;
export type VoterUpdate = z.infer<typeof VoterUpdateSchema>;
export type VoterSearchFilters = z.infer<typeof VoterSearchFiltersSchema>;

export type UserInput = z.infer<typeof UserInputSchema>;
export type UserUpdate = z.infer<typeof UserUpdateSchema>;
export type UserLogin = z.infer<typeof UserLoginSchema>;
export type UserChangePassword = z.infer<typeof UserChangePasswordSchema>;

export type ConfigOptionInput = z.infer<typeof ConfigOptionInputSchema>;
export type ConfigOptionUpdate = z.infer<typeof ConfigOptionUpdateSchema>;

export type TransactionInput = z.infer<typeof TransactionInputSchema>;
export type TransactionUpdate = z.infer<typeof TransactionUpdateSchema>;

export type VoterTurnoutInput = z.infer<typeof VoterTurnoutInputSchema>;
export type VoterTurnoutUpdate = z.infer<typeof VoterTurnoutUpdateSchema>;

export type ExportRequest = z.infer<typeof ExportRequestSchema>;
export type ImportRequest = z.infer<typeof ImportRequestSchema>;
export type ImportStatus = z.infer<typeof ImportStatusSchema>;

export type SearchRequest = z.infer<typeof SearchRequestSchema>;

export type PaginatedResponse<T = any> = z.infer<typeof PaginatedResponseSchema> & { data: T[] };
export type SuccessResponse<T = any> = z.infer<typeof SuccessResponseSchema> & { data?: T };