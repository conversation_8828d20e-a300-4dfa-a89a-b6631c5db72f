import { z } from 'zod'

// Import worker IPC schemas
// Follows project rules: Zod validation at IPC boundary, lowerCamelCase DTOs; accepts snake_case inputs too.

// Common mappings and options
export const ImportColumnMappingSchema = z.object({
  name: z.string().min(1),
  relation_type: z.string().min(1),
  relation_name: z.string().min(1),
  house_number: z.string().min(1),
  birth_year: z.union([z.number().int(), z.string().regex(/^\d{4}$/)]),
  gender: z.string().min(1),
  epic_number: z.string().min(1),
  polling_station: z.string().min(1),
  section: z.string().min(1),
}).strict()

export type ImportColumnMapping = z.infer<typeof ImportColumnMappingSchema>

export const ImportOptionsSchema = z.object({
  delimiter: z.string().default(','),
  hasHeader: z.boolean().default(true),
  encoding: z.string().default('utf-8'),
  dryRun: z.boolean().default(false),
  // On conflict with existing EPIC among active voters
  onDuplicate: z.enum(['reject', 'update', 'skip']).default('reject'),
}).partial().strict()

export type ImportOptions = z.infer<typeof ImportOptionsSchema>

// Start
export const ImportStartRequestSchema = z.object({
  filePath: z.string().min(1),
  mapping: ImportColumnMappingSchema,
  options: ImportOptionsSchema.default({}),
}).strict()

export type ImportStartRequest = z.infer<typeof ImportStartRequestSchema>

export const ImportStartResponseSchema = z.object({
  jobId: z.string().min(1),
}).strict()

export type ImportStartResponse = z.infer<typeof ImportStartResponseSchema>

// Status
export const ImportStatusRequestSchema = z.object({
  id: z.string().min(1),
}).strict()

export type ImportStatusRequest = z.infer<typeof ImportStatusRequestSchema>

export const ImportJobStatusSchema = z.object({
  id: z.string().min(1),
  status: z.enum(['pending', 'running', 'completed', 'failed', 'canceled']),
  progress: z.number().min(0).max(100),
  counters: z.object({
    processed: z.number().int().min(0),
    accepted: z.number().int().min(0),
    rejected: z.number().int().min(0),
  }).strict(),
  message: z.string().nullable(),
  errorReportPath: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
  completedAt: z.string().nullable().optional(),
}).strict()

export type ImportJobStatus = z.infer<typeof ImportJobStatusSchema>

export const ImportStatusResponseSchema = ImportJobStatusSchema
export type ImportStatusResponse = z.infer<typeof ImportStatusResponseSchema>

// Cancel
export const ImportCancelRequestSchema = z.object({
  id: z.string().min(1),
}).strict()

export type ImportCancelRequest = z.infer<typeof ImportCancelRequestSchema>

export const ImportCancelResponseSchema = z.null()
export type ImportCancelResponse = null