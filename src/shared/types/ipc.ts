/**
 * Shared IPC helpers for consistent response and error mapping.
 * This augments existing shared IPC types by exporting ipcEnvelope/ipcPlain.
 */
import { DomainError, domainErrorToResponse, ErrorCode } from '@/shared/types/errors'

export type Envelope<T> = {
  success: true
  data: T
  error: null
} | {
  success: false
  data: null
  error: {
    code: string
    message: string
    details?: unknown
    timestamp: string
  }
}

/**
 * Wrap a handler to always return an envelope { success, data, error }.
 * Usage in electron/main.ts:
 * ipcMain.handle('Channel', ipcEnvelope(async (req) => { ...return data... }))
 */
export function ipcEnvelope<TReq, TRes>(
  handler: (req: TReq) => Promise<TRes>
): (req: TReq) => Promise<Envelope<TRes>> {
  return async (req: TReq) => {
    try {
      const data = await handler(req)
      return { success: true, data, error: null }
    } catch (err) {
      const dErr = DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
      const resp = domainErrorToResponse(dErr)
      return {
        success: false,
        data: null,
        error: {
          code: resp.code,
          message: resp.message,
          details: resp.details,
          timestamp: new Date().toISOString(),
        },
      }
    }
  }
}

/**
 * Wrap a handler to return plain data for success and throw Error for failures.
 * Error message formatted as "CODE: message", and attaches .code and .details.
 * Usage in electron/main.ts:
 * ipcMain.handle('Channel', ipcPlain(async (req) => { ...return data... }))
 */
export function ipcPlain<TReq, TRes>(
  handler: (req: TReq) => Promise<TRes>
): (req: TReq) => Promise<TRes> {
  return async (req: TReq) => {
    try {
      return await handler(req)
    } catch (err) {
      const dErr = DomainError.from(err, ErrorCode.TRANSIENT_FAILURE)
      const resp = domainErrorToResponse(dErr)
      const e = new Error(`${resp.code}: ${resp.message}`) as any
      e.code = resp.code
      e.details = resp.details
      throw e
    }
  }
}