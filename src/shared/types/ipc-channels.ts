import { z } from 'zod';
import {
  type VoterInput,
  type VoterUpdate,
  type UserInput,
  type UserUpdate,
  type UserLogin,
  type ConfigOptionInput,
  type ConfigOptionUpdate,
  type TransactionInput,
  type TransactionUpdate,
  type VoterTurnoutInput,
  type VoterTurnoutUpdate,
  type SearchRequest,
  type ImportRequest,
  type ExportRequest,
  type PollingStationInput,
  type PollingStationUpdate,
  type SectionInput,
  type SectionUpdate,
  VoterInputSchema,
  VoterUpdateSchema,
  UserInputSchema,
  UserUpdateSchema,
  UserLoginSchema,
  ConfigOptionInputSchema,
  ConfigOptionUpdateSchema,
  TransactionInputSchema,
  TransactionUpdateSchema,
  VoterTurnoutInputSchema,
  VoterTurnoutUpdateSchema,
  SearchRequestSchema,
  ImportRequestSchema,
  ExportRequestSchema,
  PollingStationInputSchema,
  PollingStationUpdateSchema,
  SectionInputSchema,
  SectionUpdateSchema,
} from './schemas';
import {
  ImportStartRequestSchema,
  ImportStartResponseSchema,
  ImportStatusRequestSchema,
  ImportStatusResponseSchema,
  ImportCancelRequestSchema,
  ImportCancelResponseSchema,
  type ImportStartRequest,
  type ImportStartResponse,
  type ImportStatusRequest,
  type ImportStatusResponse,
  type ImportCancelRequest,
  type ImportCancelResponse,
} from './ipc-import';
import {
  ExportStartRequestSchema,
  ExportStatusRequestSchema,
  ExportCancelRequestSchema,
} from './ipc-export';

// Define the structure for all IPC channels
// Each channel has a request and response type
export interface IPCChannels {
  // Voter channels
  'Voter.Search': {
    request: SearchRequest;
    response: {
      success: boolean;
      data?: {
        voters: any[];
        total: number;
        page: number;
        limit: number;
        hasMore: boolean;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Voter.GetById': {
    request: { id: string };
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Voter.Upsert': {
    request: VoterInput | VoterUpdate;
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Voter.Delete': {
    request: { id: string };
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Voter.GetByPollingStation': {
    request: { pollingStationId: string; sectionId?: string };
    response: {
      success: boolean;
      data?: any[];
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // User channels
  'User.Login': {
    request: UserLogin;
    response: {
      success: boolean;
      data?: {
        user: any;
        token: string;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'User.Logout': {
    request: {};
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'User.GetById': {
    request: { id: string };
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'User.Upsert': {
    request: UserInput | UserUpdate;
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'User.Delete': {
    request: { id: string };
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'User.GetAll': {
    request: {};
    response: {
      success: boolean;
      data?: any[];
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // Config Option channels
  'ConfigOption.GetByCategory': {
    request: { category: string };
    response: {
      success: boolean;
      data?: any[];
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'ConfigOption.Upsert': {
    request: ConfigOptionInput | ConfigOptionUpdate;
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'ConfigOption.Delete': {
    request: { id: string };
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // Transaction channels
  'Transaction.GetByVoter': {
    request: { voterId: string; page?: number; limit?: number };
    response: {
      success: boolean;
      data?: {
        transactions: any[];
        total: number;
        page: number;
        limit: number;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Transaction.Upsert': {
    request: TransactionInput | TransactionUpdate;
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Transaction.Delete': {
    request: { id: string };
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // Voter Turnout channels
  'VoterTurnout.GetByVoter': {
    request: { voterId: string };
    response: {
      success: boolean;
      data?: any[];
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'VoterTurnout.Upsert': {
    request: VoterTurnoutInput | VoterTurnoutUpdate;
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'VoterTurnout.Delete': {
    request: { id: string };
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // Polling Station channels
  'PollingStation.GetAll': {
    request: {};
    response: {
      success: boolean;
      data?: any[];
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'PollingStation.GetById': {
    request: { id: string };
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'PollingStation.Upsert': {
    request: PollingStationInput | PollingStationUpdate;
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'PollingStation.Delete': {
    request: { id: string };
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // Section channels
  'Section.GetByPollingStation': {
    request: { pollingStationId: string };
    response: {
      success: boolean;
      data?: any[];
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Section.GetById': {
    request: { id: string };
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Section.Upsert': {
    request: SectionInput | SectionUpdate;
    response: {
      success: boolean;
      data?: any;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Section.Delete': {
    request: { id: string };
    response: {
      success: boolean;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // Import/Export channels
  'Import.Start': {
    request: ImportStartRequest;
    response: {
      success: boolean;
      data?: ImportStartResponse;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Import.Status': {
    request: ImportStatusRequest;
    response: {
      success: boolean;
      data?: ImportStatusResponse;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Import.Cancel': {
    request: ImportCancelRequest;
    response: {
      success: boolean;
      data?: ImportCancelResponse;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Export.Start': {
    request: z.infer<typeof ExportStartRequestSchema>;
    response: {
      success: boolean;
      data?: { jobId: string };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Export.Status': {
    request: z.infer<typeof ExportStatusRequestSchema>;
    response: {
      success: boolean;
      data?: {
        id: string;
        status: 'pending' | 'running' | 'completed' | 'failed' | 'canceled';
        progress: number;
        message: string | null;
        resultPath: string | null;
        errorReportPath?: string | null;
        createdAt: string;
        updatedAt: string;
        completedAt?: string | null;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Export.Cancel': {
    request: z.infer<typeof ExportCancelRequestSchema>;
    response: {
      success: boolean;
      data?: null;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Job.Status': {
    request: { id: string };
    response: {
      success: boolean;
      data?: {
        id: string;
        status: string;
        progress: number;
        message?: string;
        result?: any;
        error?: string;
        createdAt: string;
        updatedAt: string;
        completedAt?: string | null;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };

  // System channels
  'System.Info': {
    request: {};
    response: {
      success: boolean;
      data?: {
        platform: string;
        arch: string;
        version: string;
        versions: Record<string, string>;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
  'Database.Stats': {
    request: {};
    response: {
      success: boolean;
      data?: {
        voters: number;
        pollingStations: number;
        sections: number;
        transactions: number;
        turnout: number;
        configOptions: number;
        users: number;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    };
  };
}

// Helper types for IPC channel handling
export type ChannelName = keyof IPCChannels;
export type ChannelRequest<T extends ChannelName> = IPCChannels[T]['request'];
export type ChannelResponse<T extends ChannelName> = IPCChannels[T]['response'];

// Helper type for handler functions
export type IPCHandler<T extends ChannelName> = (
  request: ChannelRequest<T>
) => Promise<ChannelResponse<T>>;

// Helper type for preload script API
export type IPCAPI = {
  [K in ChannelName]: {
    invoke: (request: ChannelRequest<K>) => Promise<ChannelResponse<K>>;
    on: (event: string, callback: Function) => void;
    removeAllListeners: (event: string) => void;
  };
};

// Validation schemas for IPC requests
export const IPCRequestSchemas = {
  'Voter.Search': SearchRequestSchema,
  'Voter.GetById': z.object({ id: z.string().min(1) }),
  'Voter.Upsert': z.union([VoterInputSchema, VoterUpdateSchema]),
  'Voter.Delete': z.object({ id: z.string().min(1) }),
  'Voter.GetByPollingStation': z.object({
    pollingStationId: z.string().min(1),
    sectionId: z.string().optional(),
  }),
  'User.Login': UserLoginSchema,
  'User.Logout': z.object({}),
  'User.GetById': z.object({ id: z.string().min(1) }),
  'User.Upsert': z.union([UserInputSchema, UserUpdateSchema]),
  'User.Delete': z.object({ id: z.string().min(1) }),
  'User.GetAll': z.object({}),
  'ConfigOption.GetByCategory': z.object({ category: z.string().min(1) }),
  'ConfigOption.Upsert': z.union([ConfigOptionInputSchema, ConfigOptionUpdateSchema]),
  'ConfigOption.Delete': z.object({ id: z.string().min(1) }),
  'Transaction.GetByVoter': z.object({
    voterId: z.string().min(1),
    page: z.number().min(1).default(1),
    limit: z.number().min(1).max(100).default(50),
  }),
  'Transaction.Upsert': z.union([TransactionInputSchema, TransactionUpdateSchema]),
  'Transaction.Delete': z.object({ id: z.string().min(1) }),
  'VoterTurnout.GetByVoter': z.object({ voterId: z.string().min(1) }),
  'VoterTurnout.Upsert': z.union([VoterTurnoutInputSchema, VoterTurnoutUpdateSchema]),
  'VoterTurnout.Delete': z.object({ id: z.string().min(1) }),
  'PollingStation.GetAll': z.object({}),
  'PollingStation.GetById': z.object({ id: z.string().min(1) }),
  'PollingStation.Upsert': z.union([PollingStationInputSchema, PollingStationUpdateSchema]),
  'PollingStation.Delete': z.object({ id: z.string().min(1) }),
  'Section.GetByPollingStation': z.object({ pollingStationId: z.string().min(1) }),
  'Section.GetById': z.object({ id: z.string().min(1) }),
  'Section.Upsert': z.union([SectionInputSchema, SectionUpdateSchema]),
  'Section.Delete': z.object({ id: z.string().min(1) }),
  'Import.Start': ImportStartRequestSchema,
  'Import.Status': ImportStatusRequestSchema,
  'Import.Cancel': ImportCancelRequestSchema,
  'Export.Start': ExportStartRequestSchema,
  'Export.Status': ExportStatusRequestSchema,
  'Export.Cancel': ExportCancelRequestSchema,
  // Inline JobStatus request
  'Job.Status': z.object({ id: z.string().min(1) }),
  'System.Info': z.object({}),
  'Database.Stats': z.object({}),
} as const;

// Helper function to validate IPC requests
export function validateIPCRequest<T extends ChannelName>(
  channel: T,
  request: unknown
): asserts request is ChannelRequest<T> {
  const schema = IPCRequestSchemas[channel];
  if (!schema) {
    throw new Error(`No validation schema found for channel: ${channel}`);
  }

  const result = schema.safeParse(request);
  if (!result.success) {
    throw new Error(`Invalid request for channel ${channel}: ${result.error.message}`);
  }
}