import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { z } from 'zod';
import { ipcVoterLoadPage, ipcVoterUpsert } from '@/renderer/ipc/client';

// Minimal voter schema per requirements
export const zVoterStatus = z.enum([
  'Active',
  'Expired',
  'Shifted',
  'Missing',
  'Duplicate',
  'Disqualified',
]);
export type VoterStatus = z.infer<typeof zVoterStatus>;

export const zVoter = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  relationType: z.string().optional(),
  relationName: z.string().optional(),
  houseNumber: z.string().optional(),
  birthYear: z.number().int().min(1900).max(new Date().getFullYear() - 18).optional(),
  gender: z.enum(['M', 'F', 'O']).optional(),
  epicNumber: z
    .string()
    .regex(/^[A-Z]{3}\d{7}$/, 'Invalid EPIC format **********')
    .optional(),
  pollingStationId: z.string().min(1),
  sectionId: z.string().optional().nullable(),
  status: zVoterStatus.default('Active'),
  supporterStatus: z.string().optional(), // comes from config_options
  deletedAt: z.number().int().nullable().default(null),
  createdAt: z.number().int(),
  updatedAt: z.number().int(),
});
export type Voter = z.infer<typeof zVoter>;

export const zVoterUpsertInput = zVoter.partial().extend({
  id: z.string().min(1).optional(), // allow client temp ids
  name: z.string().min(1).optional(),
  pollingStationId: z.string().min(1).optional(),
});
export type VoterUpsertInput = z.infer<typeof zVoterUpsertInput>;

export interface VoterActions {
  loadPage: (params: { offset: number; limit: number }) => Promise<void>;
  setManyFromIpc: (input: unknown) => void;
  upsertOptimistic: (temp: Omit<Voter, 'id' | 'createdAt' | 'updatedAt'> & { id?: string }) => string;
  commitUpsert: (input: unknown, tempId?: string) => Promise<void>;
  revertOptimistic: (tempId: string) => void;
  clear: () => void;
}

export type VoterStore = {
  ids: string[];
  byId: Record<string, Voter>;
  pending: Record<string, Voter>; // optimistic entries by temp id
  busy: boolean;
  lastError: { code: string; message: string } | null;
  total: number;
} & VoterActions;

const initialState: Omit<VoterStore, keyof VoterActions> = {
  ids: [],
  byId: {},
  pending: {},
  busy: false,
  lastError: null,
  total: 0,
};

function toErr(e: unknown): { code: string; message: string } {
  const code = (e as any)?.code ?? 'VOTER_ERROR';
  const message = (e as any)?.message ?? 'Voter operation failed';
  return { code, message };
}

function mergeMany(current: Record<string, Voter>, list: Voter[]): Record<string, Voter> {
  const next = { ...current };
  for (const v of list) {
    next[v.id] = v;
  }
  return next;
}

export const useVoterStore = create<VoterStore>()(
  devtools((set, get) => ({
    ...initialState,
    setManyFromIpc: (input: unknown) => {
      const arr = z.array(zVoter).parse(input);
      const byId = mergeMany(get().byId, arr);
      const ids = Array.from(new Set([...get().ids, ...arr.map(v => v.id)]));
      set({ byId, ids });
    },
    loadPage: async (params) => {
      set({ busy: true, lastError: null });
      try {
        const res = await ipcVoterLoadPage(params);
        const parsed = z.object({ items: z.array(zVoter), total: z.number().int().nonnegative() }).parse(res);
        const byId = mergeMany(get().byId, parsed.items);
        const pageIds = parsed.items.map(v => v.id);
        const ids = Array.from(new Set([...get().ids, ...pageIds]));
        set({ byId, ids, total: parsed.total });
      } catch (e) {
        set({ lastError: toErr(e) });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    upsertOptimistic: (temp) => {
      const tempId = temp.id ?? `tmp_${crypto.randomUUID()}`;
      const now = Date.now();
      const optimistic = zVoter.parse({
        id: tempId,
        name: temp.name ?? 'Unnamed',
        pollingStationId: temp.pollingStationId ?? 'unknown',
        status: temp.status ?? 'Active',
        relationType: temp.relationType,
        relationName: temp.relationName,
        houseNumber: temp.houseNumber,
        birthYear: temp.birthYear,
        gender: temp.gender,
        epicNumber: temp.epicNumber,
        sectionId: temp.sectionId ?? null,
        supporterStatus: temp.supporterStatus,
        deletedAt: null,
        createdAt: now,
        updatedAt: now,
      });
      set(s => ({
        pending: { ...s.pending, [tempId]: optimistic },
        byId: { ...s.byId, [tempId]: optimistic },
        ids: s.ids.includes(tempId) ? s.ids : [tempId, ...s.ids],
      }));
      return tempId;
    },
    commitUpsert: async (input, tempId) => {
      set({ busy: true });
      try {
        const parsedInput = zVoterUpsertInput.parse(input);
        const savedUnknown = await ipcVoterUpsert(parsedInput);
        const saved = zVoter.parse(savedUnknown);
        set(s => {
          const byId = { ...s.byId, [saved.id]: saved };
          const ids = s.ids.includes(saved.id) ? s.ids : [saved.id, ...s.ids];
          if (tempId && s.pending[tempId]) {
            delete s.pending[tempId];
            delete byId[tempId];
            const idx = ids.indexOf(tempId);
            if (idx >= 0) {
              ids.splice(idx, 1);
            }
          }
          return { byId, ids };
        });
      } catch (e) {
        set({ lastError: toErr(e) });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    revertOptimistic: (tempId) => {
      set(s => {
        if (!s.pending[tempId]) return {};
        const nextPending = { ...s.pending };
        delete nextPending[tempId];
        const nextById = { ...s.byId };
        delete nextById[tempId];
        const nextIds = s.ids.filter(id => id !== tempId);
        return { pending: nextPending, byId: nextById, ids: nextIds };
      });
    },
    clear: () => set({ ...initialState }),
  }))
);