import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { z } from 'zod';
import { ipcConfigListAll, ipcConfigListByCategory, ipcConfigUpsert, ipcConfigDeactivate } from '@/renderer/ipc/client';

export const zConfigCategory = z.enum([
  'education',
  'occupation',
  'community',
  'religion',
  'economic_status',
  'supporter_status',
  'transaction_purpose',
]);
export type ConfigCategory = z.infer<typeof zConfigCategory>;

export const zConfigValue = z.object({
  id: z.string().min(1),
  category: zConfigCategory,
  value: z.string().min(1),
  order: z.number().int().nonnegative().default(0),
  active: z.boolean().default(true),
});
export type ConfigValue = z.infer<typeof zConfigValue>;

export type ConfigByCategory = Record<ConfigCategory, ConfigValue[]>;

export const zConfigByCategory = z.record(zConfigCategory, z.array(zConfigValue));
// Zod doesn't support .partial() on ZodRecord directly; emulate a partial by allowing undefined arrays.
export const zPartialConfigByCategory = z.record(zConfigCategory, z.array(zConfigValue).optional());

export interface ConfigActions {
  loadAll: () => Promise<void>;
  loadCategory: (category: ConfigCategory) => Promise<void>;
  upsertValue: (input: unknown) => Promise<void>;
  deactivateValue: (id: string) => Promise<void>;
  reorder: (category: ConfigCategory, orderedIds: string[]) => Promise<void>;
  setFromIpc: (input: unknown) => void;
  clearError: () => void;
}

export type ConfigStore = {
  byCategory: Partial<ConfigByCategory>;
  busy: boolean;
  lastError: { code: string; message: string } | null;
} & ConfigActions;

const emptyByCategory: Partial<ConfigByCategory> = {};
const initialState: Omit<ConfigStore, keyof ConfigActions> = {
  byCategory: emptyByCategory,
  busy: false,
  lastError: null,
};

function toErr(e: unknown): { code: string; message: string } {
  const code = (e as any)?.code ?? 'CONFIG_ERROR';
  const message = (e as any)?.message ?? 'Configuration operation failed';
  return { code, message };
}

function sortValues(list: ConfigValue[]): ConfigValue[] {
  return [...list].sort((a, b) => a.order - b.order || a.value.localeCompare(b.value));
}

export const useConfigStore = create<ConfigStore>()(
  devtools((set, get) => ({
    ...initialState,
    setFromIpc: (input: unknown) => {
      const parsed = zPartialConfigByCategory.parse(input);
      const sorted: Partial<ConfigByCategory> = {};
      for (const k of Object.keys(parsed) as ConfigCategory[]) {
        const list = (parsed as any)[k] as ConfigValue[] | undefined;
        if (list) sorted[k] = sortValues(list);
      }
      set({ byCategory: sorted, lastError: null });
    },
    loadAll: async () => {
      set({ busy: true, lastError: null });
      try {
        const data = await ipcConfigListAll(zConfigCategory.options as ConfigCategory[]);
        const parsed = zPartialConfigByCategory.parse(data);
        const sorted: Partial<ConfigByCategory> = {};
        for (const k of Object.keys(parsed) as ConfigCategory[]) {
          const list = (parsed as any)[k] as ConfigValue[] | undefined;
          if (list) sorted[k] = sortValues(list);
        }
        set({ byCategory: sorted });
      } catch (e) {
        set({ lastError: toErr(e) });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    loadCategory: async (category) => {
      set({ busy: true, lastError: null });
      try {
        const data = await ipcConfigListByCategory(category);
        const list = z.array(zConfigValue).parse(data);
        const next = { ...get().byCategory, [category]: sortValues(list) } as Partial<ConfigByCategory>;
        set({ byCategory: next });
      } catch (e) {
        set({ lastError: toErr(e) });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    upsertValue: async (input) => {
      set({ busy: true, lastError: null });
      try {
        const parsed = zConfigValue.parse(input);
        const saved = zConfigValue.parse(await ipcConfigUpsert(parsed));
        const cat = saved.category;
        const existing = (get().byCategory[cat] ?? []).filter(v => v.id !== saved.id);
        const next = sortValues([...existing, saved]);
        set({ byCategory: { ...get().byCategory, [cat]: next } });
      } catch (e) {
        set({ lastError: toErr(e) });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    deactivateValue: async (id) => {
      set({ busy: true, lastError: null });
      try {
        await ipcConfigDeactivate(id);
        const byCat = { ...get().byCategory };
        for (const k of Object.keys(byCat) as ConfigCategory[]) {
          const list = byCat[k] ?? [];
          const idx = list.findIndex(v => v.id === id);
          if (idx >= 0) {
            const updated = [...list];
            updated[idx] = { ...updated[idx], active: false };
            byCat[k] = updated;
          }
        }
        set({ byCategory: byCat });
      } catch (e) {
        set({ lastError: toErr(e) });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    reorder: async (category, orderedIds) => {
      // Renderer-only reorder: apply locally; persistence is handled server-side once a specific IPC is defined.
      set({ busy: true, lastError: null });
      try {
        const list = (get().byCategory[category] ?? []).slice();
        const byId = new Map(list.map(v => [v.id, v]));
        const next: ConfigValue[] = [];
        let order = 0;
        for (const id of orderedIds) {
          const v = byId.get(id);
          if (v) next.push({ ...v, order: order++ });
        }
        for (const v of list) {
          if (!orderedIds.includes(v.id)) next.push({ ...v, order: order++ });
        }
        set({ byCategory: { ...get().byCategory, [category]: sortValues(next) } });
      } catch (e) {
        set({ lastError: toErr(e) });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    clearError: () => set({ lastError: null }),
  }))
);