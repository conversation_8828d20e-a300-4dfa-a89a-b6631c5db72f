import { create } from 'zustand';
import { persist, createJSONStorage, StateStorage } from 'zustand/middleware';
import { devtools } from 'zustand/middleware';
import { z } from 'zod';
import { ipcLogin, ipcLogout } from '@/renderer/ipc/client';

export const zUserRole = z.enum(['owner', 'admin', 'editor', 'viewer']);
export type UserRole = z.infer<typeof zUserRole>;

export const zUserDTO = z.object({
  id: z.string().min(1),
  username: z.string().min(1),
  role: zUserRole,
  active: z.boolean(),
});
export type UserDTO = z.infer<typeof zUserDTO>;

export const zSession = z.object({
  user: zUserDTO.nullable(),
  lastActiveAt: z.number().int().nullable(),
});
export type SessionSnapshot = z.infer<typeof zSession>;

export const zAuthError = z.object({
  code: z.string().min(1),
  message: z.string().min(1),
  userMessage: z.string().optional(),
  details: z.record(z.string(), z.any()).optional(),
});
export type AuthError = z.infer<typeof zAuthError>;

export interface AuthActions {
  setUserFromIpc: (input: unknown) => void;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refresh: (fn: () => Promise<unknown>) => Promise<void>;
  requireRole: (minRole: UserRole) => boolean;
  clearError: () => void;
}

export type AuthStore = SessionSnapshot & {
  lastError: AuthError | null;
  busy: boolean;
} & AuthActions;

const storage: StateStorage = {
  getItem: (name: string): string | null => localStorage.getItem(name) ?? null,
  setItem: (name: string, value: string): void => localStorage.setItem(name, value),
  removeItem: (name: string): void => localStorage.removeItem(name),
};

const initialState: SessionSnapshot & { lastError: AuthError | null; busy: boolean } = {
  user: null,
  lastActiveAt: null,
  lastError: null,
  busy: false,
};

function toError(input: unknown): AuthError {
  if (typeof input === 'object' && input !== null) {
    try {
      return zAuthError.parse(input);
    } catch {
      // fallthrough
    }
  }
  const message = input instanceof Error ? input.message : typeof input === 'string' ? input : 'Unknown error';
  return { code: 'UNKNOWN', message };
}

const roleRank: Record<UserRole, number> = {
  owner: 4,
  admin: 3,
  editor: 2,
  viewer: 1,
};

export const useAuthStore = create<AuthStore>()(
  devtools(
    persist(
      (set: any, get: any) => ({
        ...initialState,
        setUserFromIpc: (input: unknown) => {
          const parsed = zUserDTO.nullable().parse(input);
          set({
            user: parsed,
            lastActiveAt: parsed ? Date.now() : null,
            lastError: null,
          });
        },
        login: async (username: string, password: string) => {
          set({ busy: true, lastError: null });
          try {
            const user = zUserDTO.parse(await ipcLogin(username, password));
            set({ user, lastActiveAt: Date.now(), lastError: null });
          } catch (e: any) {
            const err = toError(e);
            const normalized: AuthError = err.code === 'INVALID_CREDENTIALS'
              ? err
              : { ...err, code: err.code || 'INVALID_CREDENTIALS' };
            set({ lastError: normalized, user: null, lastActiveAt: null });
            throw normalized;
          } finally {
            set({ busy: false });
          }
        },
        logout: async () => {
          set({ busy: true });
          try {
            await ipcLogout();
          } finally {
            set({ user: null, lastActiveAt: null, busy: false });
          }
        },
        refresh: async (fn: () => Promise<unknown>) => {
          set({ busy: true });
          try {
            const result = await fn();
            const user = zUserDTO.nullable().parse(result);
            set({ user, lastActiveAt: user ? Date.now() : null, lastError: null });
          } catch (e) {
            set({ lastError: toError(e) });
          } finally {
            set({ busy: false });
          }
        },
        requireRole: (minRole: UserRole) => {
          const user = get().user;
          if (!user || !user.active) return false;
          return roleRank[user.role as UserRole] >= roleRank[minRole];
        },
        clearError: () => set({ lastError: null }),
      }),
      {
        name: 'auth-store',
        version: 1,
        storage: createJSONStorage(() => storage),
        migrate: (persisted: unknown) => {
          try {
            const partial = zSession.partial().parse(persisted ?? {});
            const merged = { ...initialState, ...partial };
            if (merged.user) zUserDTO.parse(merged.user);
            return merged;
          } catch {
            return { ...initialState };
          }
        },
        partialize: (state: AuthStore) => ({
          user: state.user,
          lastActiveAt: state.lastActiveAt,
        }),
      }
    )
  )
);