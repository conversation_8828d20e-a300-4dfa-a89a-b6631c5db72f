import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { z } from 'zod';
import { ipcSearchRun } from '@/renderer/ipc/client';

export const zSearchParams = z.object({
  q: z.string().trim().default(''),
  pollingStationId: z.string().min(1).optional(),
  sectionId: z.string().min(1).optional(),
  unassignedOnly: z.boolean().optional().default(false),
});
export type SearchParams = z.infer<typeof zSearchParams>;

export const zSearchResult = z.object({
  ids: z.array(z.string().min(1)),
  total: z.number().int().nonnegative(),
  latencyMs: z.number().int().nonnegative(),
});
export type SearchResult = z.infer<typeof zSearchResult>;

export interface SearchActions {
  setParams: (updater: Partial<SearchParams> | ((prev: SearchParams) => Partial<SearchParams>)) => void;
  runSearch: () => Promise<void>;
  setResultsFromIpc: (input: unknown) => void;
  clear: () => void;
}

export type SearchStore = {
  params: SearchParams;
  result: SearchResult | null;
  busy: boolean;
  lastError: { code: string; message: string } | null;
} & SearchActions;

const initialParams: SearchParams = zSearchParams.parse({});
const initialState: Omit<SearchStore, keyof SearchActions> = {
  params: initialParams,
  result: null,
  busy: false,
  lastError: null,
};

export const useSearchStore = create<SearchStore>()(
  devtools((set, get) => ({
    ...initialState,
    setParams: (updater) => {
      const prev = get().params;
      const patch = typeof updater === 'function' ? updater(prev) : updater;
      const merged = { ...prev, ...patch };
      const parsed = zSearchParams.parse(merged);
      set({ params: parsed });
    },
    runSearch: async () => {
      set({ busy: true, lastError: null });
      try {
        const params = get().params;
        const data = await ipcSearchRun(params);
        const parsed = zSearchResult.parse(data);
        set({ result: parsed });
      } catch (e: any) {
        const message = e?.message || 'Search failed';
        set({ lastError: { code: e?.code || 'SEARCH_FAILED', message } });
        throw e;
      } finally {
        set({ busy: false });
      }
    },
    setResultsFromIpc: (input) => {
      const parsed = zSearchResult.parse(input);
      set({ result: parsed });
    },
    clear: () => set({ ...initialState }),
  }))
);