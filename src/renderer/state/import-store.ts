import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { z } from 'zod';
import {
  zImportStartRequest,
  zImportStartResponse,
  zImportProgressEvent,
  zImportCancelRequest,
  zImportCancelResponse,
  zImportResult,
  type ImportStartRequest,
  type ImportStartResponse,
  type ImportProgressEvent,
  type ImportCancelRequest,
  type ImportCancelResponse,
  type ImportResult,
} from '@/shared/types/ipc-import';
import { importStart, onImportProgress, onImportResult, importCancel } from '@/renderer/ipc/client';

export const zImportState = z.object({
  importId: z.string().min(1).optional(),
  filePath: z.string().min(1).optional(),
  checkpointEvery: z.number().int().positive().max(10_000).default(100),
  allowLegacyEpicFormat: z.boolean().default(false),

  // live metrics
  processed: z.number().int().nonnegative().default(0),
  errors: z.number().int().nonnegative().default(0),
  duplicates: z.number().int().nonnegative().default(0),
  lastRowNo: z.number().int().nonnegative().default(0),
  updatedAt: z.number().int().nonnegative().default(0),

  // final summary
  result: zImportResult.optional(),
  startedAt: z.number().int().nonnegative().optional(),
  finishedAt: z.number().int().nonnegative().optional(),

  // flags
  busy: z.boolean().default(false),
  cancelled: z.boolean().default(false),
  subscribed: z.boolean().default(false),
});

export type ImportState = z.infer<typeof zImportState>;

export interface ImportActions {
  configure: (cfg: Partial<Pick<ImportStartRequest, 'filePath' | 'checkpointEvery' | 'allowLegacyEpicFormat'>> & { importId?: string }) => void;
  start: () => Promise<ImportStartResponse>;
  cancel: () => Promise<ImportCancelResponse>;
  clear: () => void;
}

export type ImportStore = ImportState & ImportActions;

// Unsub holders for event listeners
let unsubProgress: (() => void) | null = null;
let unsubResult: (() => void) | null = null;

const initialState: ImportState = zImportState.parse({
  checkpointEvery: 100,
  allowLegacyEpicFormat: false,
  processed: 0,
  errors: 0,
  duplicates: 0,
  lastRowNo: 0,
  updatedAt: 0,
  busy: false,
  cancelled: false,
  subscribed: false,
});

export const useImportStore = create<ImportStore>()(
  devtools((set, get) => ({
    ...initialState,

    configure: (cfg) => {
      const next = {
        importId: cfg.importId ?? get().importId ?? crypto.randomUUID(),
        filePath: cfg.filePath ?? get().filePath,
        checkpointEvery: cfg.checkpointEvery ?? get().checkpointEvery,
        allowLegacyEpicFormat: cfg.allowLegacyEpicFormat ?? get().allowLegacyEpicFormat,
      };
      const parsed = zImportStartRequest.partial().parse(next);
      set({ ...get(), ...parsed });
    },

    start: async () => {
      // Validate required fields
      const importId = get().importId ?? crypto.randomUUID();
      const req: ImportStartRequest = {
        importId,
        filePath: z.string().min(1).parse(get().filePath),
        checkpointEvery: get().checkpointEvery ?? 100,
        allowLegacyEpicFormat: get().allowLegacyEpicFormat ?? false,
      };
      zImportStartRequest.parse(req);

      // Subscribe to events once per run
      if (!get().subscribed) {
        unsubProgress = onImportProgress((ev: ImportProgressEvent) => {
          if (ev.importId !== get().importId) return;
          const parsed = zImportProgressEvent.parse(ev);
          set({
            processed: parsed.processed,
            errors: parsed.errors,
            duplicates: parsed.duplicates,
            lastRowNo: parsed.lastRowNo,
            updatedAt: parsed.updatedAt,
          });
        });

        unsubResult = onImportResult((res: ImportResult) => {
          if (res.importId !== get().importId) return;
          const parsed = zImportResult.parse(res);
          set({
            result: parsed,
            finishedAt: Date.now(),
            busy: false,
          });
        });
      }

      set({
        importId,
        processed: 0,
        errors: 0,
        duplicates: 0,
        lastRowNo: 0,
        updatedAt: Date.now(),
        result: undefined,
        startedAt: Date.now(),
        finishedAt: undefined,
        busy: true,
        cancelled: false,
        subscribed: true,
      });

      const resp = await importStart(req);
      const parsed = zImportStartResponse.parse(resp);
      // keep state; startedAt set above
      return parsed;
    },

    cancel: async () => {
      const importId = z.string().min(1).parse(get().importId);
      const req: ImportCancelRequest = { importId };
      const res = await importCancel(req);
      const parsed = zImportCancelResponse.parse(res);
      set({ cancelled: parsed.cancelled, busy: false });

      // Do not immediately unsubscribe; allow any final in-flight result to arrive.
      return parsed;
    },

    clear: () => {
      // Unsubscribe listeners if present
      try {
        if (unsubProgress) unsubProgress();
        if (unsubResult) unsubResult();
      } finally {
        unsubProgress = null;
        unsubResult = null;
      }
      set({ ...initialState });
    },
  }))
);