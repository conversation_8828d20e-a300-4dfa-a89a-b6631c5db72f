import React, { useState } from 'react';
import { useSearchStore } from '@/renderer/state/search-store';

export function SearchDemo() {
  const { params, result, busy, lastError, setParams, runSearch, clear } = useSearchStore();
  const [q, setQ] = useState(params.q || '');

  const onRun = async (e?: React.FormEvent) => {
    e?.preventDefault?.();
    setParams({ q });
    await runSearch();
  };

  const onClear = () => {
    clear();
    setQ('');
  };

  return (
    <div style={{ border: '1px solid #ccc', padding: 12, borderRadius: 8, marginBottom: 12 }}>
      <h3>Search Demo</h3>
      <form onSubmit={onRun} style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
        <input
          placeholder="query"
          value={q}
          onChange={(e) => setQ(e.target.value)}
          disabled={busy}
        />
        <button type="submit" disabled={busy}>Search</button>
        <button type="button" onClick={onClear} disabled={busy}>Clear</button>
      </form>

      {busy && <p>Running search...</p>}
      {result && (
        <div style={{ marginTop: 8 }}>
          <p>Found <strong>{result.total}</strong> records in {result.latencyMs}ms</p>
          <p>First 10 IDs: {result.ids.slice(0, 10).join(', ')}{result.ids.length > 10 ? '…' : ''}</p>
        </div>
      )}
      {lastError && (
        <p style={{ color: 'crimson' }}>[{lastError.code}] {lastError.message}</p>
      )}
    </div>
  );
}

export default SearchDemo;