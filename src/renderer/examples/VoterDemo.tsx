import React from 'react';
import { useVoterStore, zVoterUpsertInput } from '@/renderer/state/voter-store';

export function VoterDemo() {
  const { ids, byId, total, busy, lastError, loadPage, upsertOptimistic, commitUpsert, revertOptimistic, clear } = useVoterStore();

  const onLoad = async () => {
    await loadPage({ offset: 0, limit: 20 });
  };

  const onCreate = async () => {
    // optimistic create with minimal fields; include required placeholders to satisfy type
    const tempId = upsertOptimistic({
      name: 'Demo User',
      pollingStationId: 'PS-1',
      status: 'Active',
      sectionId: null,
      relationType: undefined,
      relationName: undefined,
      houseNumber: undefined,
      birthYear: undefined,
      gender: undefined,
      epicNumber: undefined,
      supporterStatus: undefined,
      deletedAt: null,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    } as any);
    try {
      const input = zVoterUpsertInput.parse({
        name: 'Demo User',
        pollingStationId: 'PS-1',
        status: 'Active',
        sectionId: null,
      });
      await commitUpsert(input, tempId);
    } catch (e) {
      // rollback if failure
      revertOptimistic(tempId);
    }
  };

  const firstFive = ids.slice(0, 5).map(id => byId[id]).filter(Boolean);

  return (
    <div style={{ border: '1px solid #ccc', padding: 12, borderRadius: 8, marginBottom: 12 }}>
      <h3>Voter Demo</h3>
      <div style={{ display: 'flex', gap: 8 }}>
        <button onClick={onLoad} disabled={busy}>Load Page</button>
        <button onClick={onCreate} disabled={busy}>Create Optimistic</button>
        <button onClick={() => clear()} disabled={busy}>Clear</button>
      </div>
      {busy && <p>Working...</p>}
      <p>Total: {total} | Loaded IDs: {ids.length}</p>
      <ul>
        {firstFive.map(v => (
          <li key={v.id}>{v.id}: {v.name} (PS: {v.pollingStationId})</li>
        ))}
      </ul>
      {lastError && (
        <p style={{ color: 'crimson' }}>[{lastError.code}] {lastError.message}</p>
      )}
    </div>
  );
}

export default VoterDemo;