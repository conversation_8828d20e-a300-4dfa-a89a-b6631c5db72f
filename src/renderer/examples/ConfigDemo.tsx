import React, { useEffect, useState } from 'react';
import { useConfigStore, zConfigCategory, type ConfigCategory, zConfigValue } from '@/renderer/state/config-store';

export function ConfigDemo() {
  const { byCategory, busy, lastError, loadAll, loadCategory, upsertValue, deactivateValue } = useConfigStore();
  const [cat, setCat] = useState<ConfigCategory>((zConfigCategory.options as ConfigCategory[])[0]);
  const [newValue, setNewValue] = useState('');

  useEffect(() => {
    // initial load for all categories
    loadAll().catch(() => void 0);
  }, [loadAll]);

  const list = (byCategory[cat] ?? []).slice();

  const onReloadCategory = async () => {
    await loadCategory(cat);
  };

  const onAdd = async () => {
    if (!newValue.trim()) return;
    // Create a fake id client-side; real id will come from server
    const payload = zConfigValue.parse({
      id: crypto.randomUUID(),
      category: cat,
      value: newValue.trim(),
      order: (list[list.length - 1]?.order ?? list.length - 1) + 1,
      active: true,
    });
    await upsertValue(payload).catch(() => void 0);
    setNewValue('');
  };

  const onDeactivate = async (id: string) => {
    await deactivateValue(id).catch(() => void 0);
  };

  return (
    <div style={{ border: '1px solid #ccc', padding: 12, borderRadius: 8, marginBottom: 12 }}>
      <h3>Config Demo</h3>
      <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginBottom: 8 }}>
        <select value={cat} onChange={(e) => setCat(e.target.value as ConfigCategory)} disabled={busy}>
          {(zConfigCategory.options as ConfigCategory[]).map(c => (
            <option key={c} value={c}>{c}</option>
          ))}
        </select>
        <button onClick={onReloadCategory} disabled={busy}>Reload Category</button>
      </div>

      <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginBottom: 8 }}>
        <input
          placeholder={`Add ${cat} value`}
          value={newValue}
          onChange={(e) => setNewValue(e.target.value)}
          disabled={busy}
        />
        <button onClick={onAdd} disabled={busy || !newValue.trim()}>Add</button>
      </div>

      {busy && <p>Loading...</p>}
      {lastError && <p style={{ color: 'crimson' }}>[{lastError.code}] {lastError.message}</p>}

      <ul>
        {list.map(v => (
          <li key={v.id}>
            {v.value} #{v.order} {v.active ? '' : '(inactive)'}
            &nbsp;
            {v.active && (
              <button onClick={() => onDeactivate(v.id)} disabled={busy}>Deactivate</button>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default ConfigDemo;