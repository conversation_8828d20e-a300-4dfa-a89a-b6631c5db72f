import React, { useCallback, useMemo, useState } from 'react';
import { useImportStore } from '@/renderer/state/import-store';

export function ImportDemo() {
  const {
    importId,
    filePath,
    checkpointEvery,
    allowLegacyEpicFormat,
    processed,
    errors,
    duplicates,
    lastRowNo,
    updatedAt,
    result,
    startedAt,
    finishedAt,
    busy,
    cancelled,
    configure,
    start,
    cancel,
    clear,
  } = useImportStore();

  const [pathInput, setPathInput] = useState(filePath ?? '');
  const [checkpointInput, setCheckpointInput] = useState(checkpointEvery ?? 100);
  const [allowLegacy, setAllowLegacy] = useState(allowLegacyEpicFormat ?? false);

  const canStart = useMemo(() => {
    return !!pathInput && !busy;
  }, [pathInput, busy]);

  const onConfigure = useCallback(() => {
    configure({
      importId,
      filePath: pathInput.trim(),
      checkpointEvery: checkpointInput,
      allowLegacyEpicFormat: allowLegacy,
    });
  }, [configure, importId, pathInput, checkpointInput, allowLegacy]);

  const onStart = useCallback(async () => {
    onConfigure();
    await start();
  }, [onConfigure, start]);

  const onCancel = useCallback(async () => {
    await cancel();
  }, [cancel]);

  const onClear = useCallback(() => {
    clear();
    setPathInput('');
    setCheckpointInput(100);
    setAllowLegacy(false);
  }, [clear]);

  return (
    <div style={{ padding: 16, fontFamily: 'sans-serif', maxWidth: 800 }}>
      <h2>CSV Import Demo</h2>

      <section style={{ marginBottom: 12, padding: 12, border: '1px solid #ddd', borderRadius: 6 }}>
        <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginBottom: 8 }}>
          <label style={{ width: 120 }}>CSV Path</label>
          <input
            type="text"
            placeholder="/absolute/path/to/file.csv"
            value={pathInput}
            onChange={e => setPathInput(e.target.value)}
            style={{ flex: 1, padding: 6 }}
          />
        </div>

        <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginBottom: 8 }}>
          <label style={{ width: 120 }}>Checkpoint Every</label>
          <input
            type="number"
            value={checkpointInput}
            min={1}
            max={10000}
            onChange={e => setCheckpointInput(Math.max(1, Math.min(10000, Number(e.target.value) || 1)))}
            style={{ width: 120, padding: 6 }}
          />
        </div>

        <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginBottom: 8 }}>
          <label style={{ width: 120 }}>Allow Legacy EPIC</label>
          <input
            type="checkbox"
            checked={allowLegacy}
            onChange={e => setAllowLegacy(e.target.checked)}
          />
        </div>

        <div style={{ display: 'flex', gap: 8 }}>
          <button onClick={onConfigure} disabled={!pathInput} style={{ padding: '6px 12px' }}>
            Configure
          </button>
          <button onClick={onStart} disabled={!canStart} style={{ padding: '6px 12px' }}>
            {busy ? 'Running…' : 'Start Import'}
          </button>
          <button onClick={onCancel} disabled={!busy} style={{ padding: '6px 12px' }}>
            Cancel
          </button>
          <button onClick={onClear} style={{ padding: '6px 12px' }}>
            Clear
          </button>
        </div>
      </section>

      <section style={{ marginBottom: 12, padding: 12, border: '1px solid #ddd', borderRadius: 6 }}>
        <h3>Live Progress</h3>
        <div style={{ display: 'grid', gridTemplateColumns: '160px 1fr', rowGap: 6 }}>
          <div>Import ID</div><div>{importId ?? '-'}</div>
          <div>File Path</div><div title={filePath}>{filePath ?? '-'}</div>
          <div>Processed</div><div>{processed}</div>
          <div>Errors</div><div>{errors}</div>
          <div>Duplicates</div><div>{duplicates}</div>
          <div>Last Row No</div><div>{lastRowNo}</div>
          <div>Updated At</div><div>{updatedAt ? new Date(updatedAt).toLocaleString() : '-'}</div>
          <div>Started</div><div>{startedAt ? new Date(startedAt).toLocaleString() : '-'}</div>
          <div>Finished</div><div>{finishedAt ? new Date(finishedAt).toLocaleString() : '-'}</div>
          <div>Busy</div><div>{busy ? 'Yes' : 'No'}</div>
          <div>Cancelled</div><div>{cancelled ? 'Yes' : 'No'}</div>
        </div>
      </section>

      <section style={{ marginBottom: 12, padding: 12, border: '1px solid #ddd', borderRadius: 6 }}>
        <h3>Final Result</h3>
        {result ? (
          <div style={{ display: 'grid', gridTemplateColumns: '160px 1fr', rowGap: 6 }}>
            <div>Import ID</div><div>{result.importId}</div>
            <div>Total Rows</div><div>{result.totalRows}</div>
            <div>Valid Rows</div><div>{result.validRows}</div>
            <div>Error Count</div><div>{result.errorCount}</div>
            <div>Duplicate Count</div><div>{result.duplicateCount}</div>
            <div>Duration (ms)</div><div>{result.durationMs}</div>
          </div>
        ) : (
          <div>—</div>
        )}
      </section>

      <p style={{ color: '#666' }}>
        Tip: You can test with the sample file at docs/voter-management-system/DB_SAMPLE.csv (provide absolute path).
      </p>
    </div>
  );
}

export default ImportDemo;