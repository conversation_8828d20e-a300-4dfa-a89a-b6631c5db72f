import React, { useState } from 'react';
import { useAuthStore } from '@/renderer/state/auth-store';

export function AuthDemo() {
  const { user, busy, lastError, login, logout, clearError } = useAuthStore();
  const [username, setUsername] = useState('owner');
  const [password, setPassword] = useState('password');

  const onLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    try {
      await login(username, password);
    } catch {
      // error surfaced in store
    }
  };

  const onLogout = async () => {
    await logout();
  };

  return (
    <div style={{ border: '1px solid #ccc', padding: 12, borderRadius: 8, marginBottom: 12 }}>
      <h3>Auth Demo</h3>
      {user ? (
        <div>
          <p>Logged in as <strong>{user.username}</strong> (role: {user.role}, active: {String(user.active)})</p>
          <button onClick={onLogout} disabled={busy}>Logout</button>
        </div>
      ) : (
        <form onSubmit={onLogin}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <input
              placeholder="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={busy}
            />
            <input
              placeholder="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={busy}
            />
            <button type="submit" disabled={busy}>Login</button>
          </div>
        </form>
      )}
      {lastError && (
        <p style={{ color: 'crimson' }}>[{lastError.code}] {lastError.message}</p>
      )}
    </div>
  );
}

export default AuthDemo;