import { z } from 'zod';
import { DomainError, ErrorCode } from '@/shared/types/errors';
import type { ChannelResponse } from '@/shared/types/ipc-channels';
import { zUserDTO } from '@/renderer/state/auth-store';
import { zSearchResult } from '@/renderer/state/search-store';
import { zConfigCategory, zConfigValue } from '@/renderer/state/config-store';
import { zVoter, zVoterUpsertInput } from '@/renderer/state/voter-store';
import type {
  ImportStartRequest,
  ImportStartResponse,
  ImportProgressEvent,
  ImportCancelRequest,
  ImportCancelResponse,
  ImportResult,
} from '@/shared/types/ipc-import';

// Helpers
function unwrapOrThrow<T>(channel: string, resp: ChannelResponse<any>): T {
  if (resp && (resp as any).success) {
    return (resp as any).data as T;
  }
  const err = (resp as any)?.error;
  throw new DomainError(
    (err?.code as ErrorCode) ?? ErrorCode.TRANSIENT_FAILURE,
    err?.message ?? `IPC ${channel} failed`
  );
}

// Auth shims
export async function ipcLogin(username: string, password: string) {
  const resp = await window.electronAPI.ipc.login({ username, password });
  const { user } = unwrapOrThrow<{ user: unknown; token?: string }>('User.Login', resp);
  return zUserDTO.parse(user);
}

export async function ipcLogout() {
  const resp = await window.electronAPI.ipc.logout({});
  unwrapOrThrow<void>('User.Logout', resp);
}

// Search shim (maps to SearchStore result)
export async function ipcSearchRun(params: { q: string; pollingStationId?: string; sectionId?: string; unassignedOnly?: boolean }) {
  const t0 = performance.now();
  const resp = await window.electronAPI.ipc.searchVoters({
    query: params.q ?? '',
    pollingStationId: params.pollingStationId,
    sectionId: params.sectionId,
    unassignedOnly: params.unassignedOnly ?? false,
    page: 1,
    limit: 100,
  } as any);
  const data = unwrapOrThrow<{
    voters: Array<{ id: string } & Record<string, unknown>>;
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  }>('Voter.Search', resp);
  const ids = (data.voters ?? []).map(v => z.string().min(1).parse(v.id));
  const latencyMs = Math.max(0, Math.round(performance.now() - t0));
  return zSearchResult.parse({ ids, total: data.total ?? 0, latencyMs });
}

// Config shims (not exposed in preload yet)
export async function ipcConfigListByCategory(_category: z.infer<typeof zConfigCategory>) {
  throw new DomainError(ErrorCode.NOT_FOUND, 'ConfigOption.* IPC methods are not exposed via preload.ipc');
}

export async function ipcConfigListAll(_categories = zConfigCategory.options as z.infer<typeof zConfigCategory>[]) {
  throw new DomainError(ErrorCode.NOT_FOUND, 'ConfigOption.* IPC methods are not exposed via preload.ipc');
}

export async function ipcConfigUpsert(_input: z.infer<typeof zConfigValue>) {
  throw new DomainError(ErrorCode.NOT_FOUND, 'ConfigOption.* IPC methods are not exposed via preload.ipc');
}

export async function ipcConfigDeactivate(_id: string) {
  throw new DomainError(ErrorCode.NOT_FOUND, 'ConfigOption.* IPC methods are not exposed via preload.ipc');
}

// Voter shims
export async function ipcVoterLoadPage(params: { offset: number; limit: number }) {
  // Translate offset/limit to page/limit for Voter.Search
  const page = Math.floor(params.offset / Math.max(1, params.limit)) + 1;
  const resp = await window.electronAPI.ipc.searchVoters({
    query: '',
    page,
    limit: params.limit,
  } as any);
  const data = unwrapOrThrow<{
    voters: any[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  }>('Voter.Search', resp);
  const items = z.array(zVoter).parse(
    (data.voters ?? []).map(v => ({
      ...v,
      pollingStationId: v.pollingStationId ?? v.polling_station_id,
      epicNumber: v.epicNumber ?? v.epic_number,
      houseNumber: v.houseNumber ?? v.house_number,
      relationType: v.relationType ?? v.relation_type,
      relationName: v.relationName ?? v.relation_name,
      createdAt: v.createdAt ?? v.created_at,
      updatedAt: v.updatedAt ?? v.updated_at,
      deletedAt: 'deletedAt' in v ? v.deletedAt : (('deleted_at' in v) ? v.deleted_at : null),
    }))
  );
  return { items, total: data.total ?? items.length };
}

export async function ipcVoterUpsert(input: z.infer<typeof zVoterUpsertInput>) {
  const resp = await window.electronAPI.ipc.upsertVoter(input as any);
  const saved = unwrapOrThrow<any>('Voter.Upsert', resp);
  return zVoter.parse({
    ...saved,
    pollingStationId: saved.pollingStationId ?? saved.polling_station_id,
    epicNumber: saved.epicNumber ?? saved.epic_number,
    houseNumber: saved.houseNumber ?? saved.house_number,
    relationType: saved.relationType ?? saved.relation_type,
    relationName: saved.relationName ?? saved.relation_name,
    createdAt: saved.createdAt ?? saved.created_at,
    updatedAt: saved.updatedAt ?? saved.updated_at,
    deletedAt: 'deletedAt' in saved ? saved.deletedAt : (('deleted_at' in saved) ? saved.deleted_at : null),
  });
}

// Import shims via secure preload API
export async function importStart(req: ImportStartRequest): Promise<ImportStartResponse> {
  return window.electronAPI.import.start(req);
}

export function onImportProgress(cb: (ev: ImportProgressEvent) => void): () => void {
  return window.electronAPI.import.onProgress(cb);
}

export function onImportResult(cb: (res: ImportResult) => void): () => void {
  return window.electronAPI.import.onResult(cb);
}

export async function importCancel(req: ImportCancelRequest): Promise<ImportCancelResponse> {
  return window.electronAPI.import.cancel(req);
}